<template>
  <Card>
    <Row>
      <Col span="4">
        <Button type="primary" @click="handleEdit">新增</Button>
      </Col>
      <Col span="20" style="text-align: right;">
        <Checkbox-group v-model="tableColumnsChecked" @on-change="changeTableColumns" style="display: inline-block;">
          <Checkbox label="add">查看新增备件</Checkbox>
          <Checkbox label="warning">显示预警</Checkbox>
          <Checkbox label="stock">显示安全库存</Checkbox>
        </Checkbox-group>
        <Dropdown @on-click="shipChange">
          <a href="javascript:void(0)" v-html="ship_name !== '' ? ship_name : '请选择'" style="color: #515a6e;"></a>
          <Icon type="md-arrow-dropdown"></Icon>
          <Dropdown-menu slot="list">
            <Dropdown-item v-for="(item, idx) in shipList" :name="item.ship_name">{{item.ship_name}}</Dropdown-item>
          </Dropdown-menu>
        </Dropdown>
        &nbsp;
        <Dropdown @on-click="equipChange">
          <a href="javascript:void(0)" v-html="equip_name !== '' ? equip_name : '请选择'" style="color: #515a6e;"></a>
          <Icon type="md-arrow-dropdown"></Icon>
          <Dropdown-menu slot="list">
            <Dropdown-item name="全部备件">全部备件</Dropdown-item>
            <Dropdown-item name="启用备件">启用备件</Dropdown-item>
            <Dropdown-item name="停用备件">停用备件</Dropdown-item>
          </Dropdown-menu>
        </Dropdown>
        <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
      </Col>
    </Row>
    <Row>
      <Col span="4" style="border: 1px solid #ccc;">
        <h3 class="col-text">船舶备件</h3>
        <Tree :data="tree" :render="renderContent" class="tree"></Tree>
      </Col>
      <Col span="20" style="padding-left: 10px;">
        <Table border :loading="loading" :columns="columns" :data="list" @on-row-click="tableClick"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </Col>
    </Row>
    <!-- 弹窗内容 -->
    <Modal v-model="modal" :title="titleModal" @on-ok="handleSubmit('formInline')" @on-cancel="handleCancel" width="70%" :mask-closable="false" id="form_items">
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="100">
        <div v-if="modalType !== 'file'" class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item prop="equip_name" label="备件名称">
                <Input type="text" v-model="formInline.equip_name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="系统编码">
                <div v-model="formInline.code" v-if="formInline.code === ''">--</div>
                <div v-model="formInline.code" v-else>{{formInline.code}}</div>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="备件状态">
                <Radio-group v-model="formInline.state" type="button">
                  <Radio label="启用">启用</Radio>
                  <Radio label="停用">停用</Radio>
                </Radio-group>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="备件号">
                <Input type="text" v-model="formInline.equip_no" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="图纸编号">
                <Input type="text" v-model="formInline.drawing_no" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="规格">
                <Input type="text" v-model="formInline.specs" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="单位">
                <Dropdown trigger="custom" :visible="visible">
                  <div :class="visible ? 'drop_down_sel sel_focu' : 'drop_down_sel'" :style="{width: `${formItemWidth}px`}">
                    <a href="javascript:void(0)" @click="handleOpen" v-html="unit_name === '' ? '请选择' : unit_name"></a>
                    <Icon type="md-arrow-dropdown" color='#333'></Icon>
                  </div>
                  <Dropdown-menu slot="list" :style="{width: `${formItemWidth}px`}">
                    <Dropdown-item v-for="(item, idx) in unitList" :name="item.name" style="padding: 7px 10px">
                      <span v-if="item.isEdit" @click="seleClick(item)" style="width: 80%; display: inline-block;">{{ item.name }}</span>
                      <span v-else><Input type="text" v-model="item.value" style="width: 80%;"></Input></span>
                      <span class="positon-span">
                        <Icon @click="editBtn(item)" type="ios-brush-outline" v-if="item.isEdit"></Icon>
                        <Icon @click="saveBtn(item)" type="md-checkmark" v-else></Icon>
                        <Icon @click="removeBtn(idx)" type="ios-trash-outline"></Icon>
                      </span>
                    </Dropdown-item>
                  </Dropdown-menu>
                </Dropdown>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="手册页码">
                <Input type="text" v-model="formInline.manual_page" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="单价">
                <Input-number v-model="formInline.price" placeholder="请输入" style="width:50%"></Input-number>
                <Select v-model="formInline.price_unit" placeholder="请选择" style="width:49%;float:right;">
                  <Option value="元">元</Option>
                  <Option value="卢布">卢布</Option>
                  <Option value="美元">美元</Option>
                  <Option value="英镑元">英镑</Option>
                  <Option value="日元">日元</Option>
                  <Option value="港元">港元</Option>
                  <Option value="欧元">欧元</Option>
                  <Option value="新西兰元">新西兰元</Option>
                  <Option value="新加坡元">新加坡元</Option>
                  <Option value="泰铢">泰铢</Option>
                </Select>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="equip_type" label="所属设备">
                <Select v-model="formInline.equip_type" placeholder="请选择">
                  <Option v-for="(item, idx) in equipTypeList" :value="item.id">{{item.name}}</Option>
                </Select>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="制造厂商">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="供应商">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="船舶库存">
                <div v-if="formInline.ship_stock === ''">--</div>
                <div v-else>{{formInline.ship_stock}}</div>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="安全库存">
                <Input-number v-model="formInline.price" placeholder="请输入"></Input-number>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="最高库存">
                <Input-number v-model="formInline.num" placeholder="请输入"></Input-number>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="船用数量">
                <Input-number v-model="formInline.quantity" placeholder="请输入"></Input-number>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Form-item label="备注" class="remak_la">
              <Input type="textarea" v-model="formInline.remark" placeholder="请输入"></Input>
            </Form-item>
          </Row>
        </div>
        <div class="tr-name" v-if="modalType !== 'file'">附件</div>
        <fileUpload ref="fileUploadComponent" :fileDataList="fileDataList" type="modalType" @getFileId="getFileId"></fileUpload>
      </Form>
    </Modal>
  </Card>
</template>
<script>
import search from '_c/search'
import fileUpload from '../../../performance/performanceTemp/fileUpload'
export default {
  components: {
    search,
    fileUpload
  },
  data () {
    return {
      visible: false,
      unit_name: '',
      formItemWidth: null,
      tableColumnsChecked: ['add', 'warning', 'stock'],
      ship_name: '',
      shipList: [{
        id: '1',
        ship_name: 'test'
      }], // 储存船名下拉
      equip_name: '',
      setSearchData: {
        member_name: {
          type: 'text',
          label: '共条结果',
          width: 180,
          value: '',
          isdisable: false,
          placeholder: '请输入备件名称、系统编码、备件型号、制造厂商'
        }
      },      
      tree: [
        {
          title: '主推及辅助备件类',
          // expand: true,
          render: (h, { root, node, data }) => {
            return h('span', {
              style: {
                display: 'inline-block',
                width: '100%'
              },
              on: {
                click: () => {
                  this.equipTreeSeled.name = data.title
                }
              }
            }, [
              h('span', [
                h('Icon', {
                  props: {
                    type: 'md-home'
                  },
                  style: {
                    marginRight: '8px'
                  }
                }),
                h('span', data.title)
              ])
            ])
          },
          children: [
            {
              title: '主机'
            },
            {
              title: '供油单元'
            },
            {
              title: '淡水冷却器'
            },
            {
              title: '滑油冷却器'
            }
          ]
        }
      ],
      loading: false,
      total: 0,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1,
      list: [{
        name: '主机',
        date: '2012-12-06'
      }],
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '系统编码',
          key: 'name',
          sortable: true,
          align: 'center'
        },
        {
          title: '备件名称',
          key: '',
          sortable: true,
          align: 'center'
        },
        {
          title: '备件号',
          key: '',
          sortable: true,
          align: 'center'
        },
        {
          title: '图纸编号',
          key: '',
          sortable: true,
          align: 'center'
        },
        {
          title: '规格型号',
          key: '',
          align: 'center'
        },
        {
          title: '制造厂商',
          key: 'date',
          sortable: true,
          align: 'center'
        },
        {
          title: '安全库存',
          key: 'level',
          align: 'center'
        },
        {
          title: '船舶库存',
          key: 'status',
          align: 'center'
        },
        {
          title: '附件',
          key: '',
          align: 'center',
          width: 120,
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-link',
                size: 'small',
                type: 'text'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.modalType = 'file'
                  this.titleModal = '附件信息'
                  this.modal = true
                  this.handleUpload(params.row.id)
                }
              }
            }, // params.row.fileArr.length
            )
          }
        },
        {
          title: '操作',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-trash',
                size: 'small'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.handleDelete(params.row)                  
                }
              }
            })
          }
        }
      ],
      modal: false,
      titleModal: '',
      formInline: {
        state: '启用',
        code: '',
        ship_stock: ''
      },
      ruleInline: {
        equip_name: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        equip_type: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      unitList: [{
        id: '1',
        unit_name: '团'
      }],
      equipTypeList: [],
      modalType: '',
      equipTreeSeled: {}, // 当前已选中的节点数组
      fileDataList: [],
      wps_ids: ''
    }
  },
  methods: {
    // 树节点
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
          click: () => {}
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', data.title)
        ])
      ])
    },
    // 获取列表
    getList (d) {
      // this.loading = true
      // API(this.listQuery).then(res => {
      //   if (res.data.Code === 10000) {
      //     this.loading = false
      //     this.list = res.data.Result
      //     this.total = res.data.Total
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    changeTableColumns () {
      this.tableColumns2 = this.getTable2Columns();
    },
    // 船名搜索
    shipChange (name) {
      if (name === '') return
      this.ship_name = name
    },
    // 备件查询
    equipChange (name) {
      this.equip_name = name
    },
    // 新增
    handleEdit () {
      this.titleModal = '新增备件基本信息'
      this.modal = true
      this.formInline.code = '--'
      this.formInline.stock = '--'
      this.formItemWidth = (document.getElementById('form_items').clientWidth * 0.7 - 12) / 2 - 138
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 保存
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认保存备件手册？</p>',
            loading: true,
            onOk: () => {
              API(this.formInline).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.$refs['name'].resetFields()
                  this.getList()
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleCancel () {
      this.modalType = ''
      this.$refs['formInline'].resetFields()
      this.formInline.unit = ''
      this.formInline.price_unit = ''
      this.formInline.equip_type = ''
      this.formInline.state = '启用'
      this.unitList = []
      this.visible = false
    },
    // 编辑
    tableClick (list) {
      this.modal = true
      this.titleModal = '编辑备件基本信息'
      this.formInline = list
      // this.fileDataList = list.files
      this.formItemWidth = (document.getElementById('form_items').clientWidth * 0.7 - 12) / 2 - 138
    },
    // 删除列表
    handleDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>删除后无法恢复，是否确认删除？</p>',
        loading: true,
        onOk: () => {
          // API.({ id: row.id }).then(res => {
          //   if (res.data.Code === 10000) {
          //     this.loading = false
          //     this.$Modal.remove()
          //     this.$Message.success(res.data.Message)
          //     this.getList(this.equipTree[0].id)
          //   } else {
          //     this.loading = false
          //     this.$Modal.remove()
          //     this.$Message.error(res.data.Message)
          //   }
          // })
        }
      })
    },
    // 附件上传 
    handleUpload (d) {},
    getFileId () {
      this.wps_ids = this.fileDataList.map(item => {
        return item.id
      }).join()
    },
    // 编辑单位下拉项
    editBtn (item) {
      item.isEdit = false
    },
    // 保存单位下拉项
    saveBtn (item) {
      item.name = item.value
      item.isEdit = true
    },
    // 删除单位下拉项
    removeBtn (index) {
      this.unitList.splice(index, 1)
    },
    // 点击单位下拉项触发
    seleClick (item) {
      this.visible = false
      this.unit_name = item.name
      this.formInline.unit = item.name
    },
    // 打开单位下拉
    handleOpen () {
      this.visible = true
    }
  }
}
</script>
<style lang="less" scoped>
.drop_down_sel {
  padding: 0 10px;
  border-radius: 5px;
  border: 1px solid #dcdee2;
  a {
    color: #666;
    width: 95%;
    display: inline-block;
  }
}
.drop_down_sel.sel_focux {
  border: 1px solid #2D8cF0;
}
.positon-span {
  text-align: right;
  margin-left: 5px;
  i {
    padding: 0 5px;
    font-size: 16px;
  }
}
.search-vie {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
</style>
<style>
.tree.ivu-tree ul li {
  margin: 5px 0;
}
.tree .ivu-tree-title span {
  font-size: 16px;
  color: #666;
}
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .remak_la .ivu-form-item-label {
  line-height: 58px;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
}
.form-class-div .ivu-input-number {
  width: 100%;
}
</style>
