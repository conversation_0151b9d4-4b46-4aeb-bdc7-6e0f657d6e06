<template>
  <Modal v-model="modalData.modal" width="600" :title="modalData.title" @on-visible-change="modalShowHide" :mask-closable="false">
    <Button type="primary" class="import_btn" @click="importPlanModal">导入</Button>
    <Form ref="formInline" :model="formData" inline :label-width="70">
      <Row>
        <Col span="12">
          <!-- v-if="modalData.modal || !importModalData.modal" -->
          <Form-item prop="plan_month" label="月份" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <DatePicker :value="formData.plan_month" type="month" placeholder="请选择月份" format="yyyy-MM" @on-change="data=>formData.plan_month=data"></DatePicker>
          </Form-item>
        </Col>
        <Col span="12">
          <Form-item prop="ship_id" label="船舶" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Select style="width: 187px;" filterable v-model="formData.ship_id" :disabled="modalData.type === 'modify'">
              <Option v-for="(item, idx) in modalData.shipList" :key="'user' + idx" :value="item.ship_id">{{ item.ship_name }}</Option>
            </Select>
          </Form-item>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <Form-item prop="loading_date" label="受载期" :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}">
            <DatePicker v-model="formData.loading_date" format="yyyy.MM.dd" type="daterange" placeholder="请选择日期"  @on-change="data=>formData.loading_date=data"></DatePicker>
          </Form-item>
        </Col>
        <Col span="12">
          <Form-item prop="voyage_no" label="航次" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Input style="width: 187px;" type='text' v-model='formData.voyage_no'>
              <span slot="prepend">V.</span>
            </Input>
          </Form-item>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <Form-item prop="goods_id" label="货品" :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}">
            <Select style="width: 187px;" multiple filterable v-model="formData.goods_id">
              <Option v-for="(item, idx) in cargoList" :key="'user' + idx" :value="item.id">{{ item.cargo_name }}</Option>
            </Select>
          </Form-item>
        </Col>
        <Col span="12">
          <Form-item prop="goods_amount" label="货量" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Input style="width: 187px;" type='text' v-model='formData.goods_amount'></Input>
          </Form-item>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <Form-item prop="load_port_id" label="装港"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}"-->
            <Select style="width: 187px;" multiple filterable v-model="formData.load_port_id" @on-change='loadPortChange'>
              <Option v-for="(item, idx) in portList" :key="'load_port' + idx" :value="item.id">{{ item.port_name }}</Option>
            </Select>
          </Form-item>
        </Col>
        <Col span="12">
          <Form-item prop="unload_port_id" label="卸港"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}"-->
            <Select style="width: 187px;" multiple filterable v-model="formData.unload_port_id" @on-change='unloadPortChange'>
              <Option v-for="(item, idx) in portList" :key="'unload_port' + idx" :value="item.id">{{ item.port_name }}</Option>
            </Select>
          </Form-item>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <Form-item prop="load_wharf_id" label="装港码头"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}"-->
            <Select style="width: 187px;" multiple filterable v-model="formData.load_wharf_id" :disabled="!loadWharfList || loadWharfList.length === 0">
              <!-- <Option v-for="(item, idx) in loadWharfList" :key="item.wharf_id" :value="item.wharf_id">{{ item.wharf_name }}</Option> -->
              <OptionGroup v-for="(list, idx) in loadWharfGroup" :label="list.port_name" :key="'load_wharf' + idx">
                <Option v-for="(item, index) in list.items" :key="item.wharf_id" :value="item.wharf_id">{{ item.wharf_name }}</Option>
              </OptionGroup>
            </Select>
          </Form-item>
        </Col>
        <Col span="12">
          <Form-item prop="unload_wharf_id" label="卸港码头"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}"-->
            <Select style="width: 187px;" multiple filterable v-model="formData.unload_wharf_id" :disabled="!unloadWharfList || unloadWharfList.length === 0">
              <!-- <Option v-for="(item, idx) in unloadWharfList" :key="item.wharf_id" :value="item.wharf_id">{{ item.wharf_name }}</Option> -->
              <OptionGroup v-for="(list, idx) in unloadWharfGroup" :label="list.port_name" :key="'unload_wharf' + idx">
                <Option v-for="(item, index) in list.items" :key="item.wharf_id" :value="item.wharf_id">{{ item.wharf_name }}</Option>
              </OptionGroup>
            </Select>
          </Form-item>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <Form-item prop="load_require" label="装港要求">
            <Input style="width: 187px;" type='text' v-model='formData.load_require'></Input>
          </Form-item>
        </Col>
        <Col span="12">
          <Form-item prop="unload_require" label="卸港要求">
            <Input style="width: 187px;" type='text' v-model='formData.unload_require'></Input>
          </Form-item>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <Form-item prop="recipient_company_id" label="客户"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <Select style="width: 187px;" filterable v-model="formData.recipient_company_id">
              <Option v-for="(item, idx) in companyList" :key="'user' + idx" :value="item.id">{{ item.name }}</Option>
            </Select>
          </Form-item>
        </Col>
        <Col span="12">
          <Form-item prop="remarks" label="备注">
            <Input style="width: 187px;" type='text' v-model='formData.remarks'></Input>
          </Form-item>
        </Col>
      </Row>
    </Form>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button @click="handleSave" type="primary">保存</Button>
    </div>
    <ImportModal :modalData="importModalData" @importBack="importBackObj"></ImportModal>
  </Modal>
</template>
<script>
import basicAPI from '@/api/basicData'
import API from '@/api/voyagePlan'
import ImportModal from './importModal'

export default {
  props: {
    modalData: Object
  },
  components: {
    ImportModal
  },
  computed: {
    loadWharfGroup() {
      // 使用 reduce 方法进行数组的拆分和分类
      return this.loadWharfList.reduce((result, item) => {
        // 查找是否已经存在该 category 的数组
        let existingGroup = result.find(group => group.port_name === item.port_name)
        if (!existingGroup) {
          // 如果不存在，创建一个新的组，并加入到结果数组中
          existingGroup = {
            port_name: item.port_name,
            items: []
          }
          result.push(existingGroup)
        }
        // 将当前 item 加入到对应的组中
        existingGroup.items.push(item)
        return result
      }, [])
    },
    unloadWharfGroup() {
      // 使用 reduce 方法进行数组的拆分和分类
      return this.unloadWharfList.reduce((result, item) => {
        // 查找是否已经存在该 category 的数组
        let existingGroup = result.find(group => group.port_name === item.port_name)
        if (!existingGroup) {
          // 如果不存在，创建一个新的组，并加入到结果数组中
          existingGroup = {
            port_name: item.port_name,
            items: []
          }
          result.push(existingGroup)
        }
        // 将当前 item 加入到对应的组中
        existingGroup.items.push(item)
        return result
      }, [])
    }
  },
  data () {
    return {
      importModalData: {
        title: '航次数据导入',
        modal: false,
        data: {}
      },
      oldData: {}, // 原始未修改数据，方便判断是否修改使用
      formData: {
        plan_month: '',
        ship_id: '',
        loading_date: '',
        voyage_no: '',
        load_port_id: '',
        unload_port_id: '',
        load_wharf_id: '',
        unload_wharf_id: '',
        goods_id: '',
        goods_amount: '',
        load_require: '',
        unload_require: '',
        recipient_company_id: '',
        remarks: ''
      },
      portList: [], // 港口列表
      loadWharfList: [], // 装港码头列表
      unloadWharfList: [], // 卸港码头列表
      cargoList: [], // 货品列表
      companyList: [] // 货主列表
    }
  },
  methods: {
    handleSave () { // 保存
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.modalData.type === 'add') {
            let _param = {...{}, ...this.formData}
            _param.loading_date = JSON.stringify(_param.loading_date)
            _param.load_port_ids = _param.load_port_id ? _param.load_port_id.join() : ''
            _param.unload_port_ids = _param.unload_port_id ? _param.unload_port_id.join() : ''
            _param.load_wharf_ids = _param.load_wharf_id ? _param.load_wharf_id.join() : ''
            _param.unload_wharf_ids = _param.unload_wharf_id ? _param.unload_wharf_id.join() : ''
            _param.goods_ids = _param.goods_id.join()
            API.addVmpPlan(_param).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.clearData()
                this.$emit('planBack')
                this.modalData.modal = false
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
          if (this.modalData.type === 'modify') {
            let _param = {...{}, ...this.formData}
            _param.loading_date = JSON.stringify(_param.loading_date)
            _param.load_port_ids = _param.load_port_id ? _param.load_port_id.join() : ''
            _param.unload_port_ids = _param.unload_port_id ? _param.unload_port_id.join() : ''
            _param.load_wharf_ids = _param.load_wharf_id ? _param.load_wharf_id.join() : ''
            _param.unload_wharf_ids = _param.unload_wharf_id ? _param.unload_wharf_id.join() : ''
            _param.goods_ids = _param.goods_id.join()
            if (typeof this.formData.plan_month === 'object') { // plan_month日期转换
              let planDate = new Date(this.formData.plan_month)
              let planYear = planDate.getFullYear()
              let planMonth = (planDate.getMonth() + 1) > 9 ? (planDate.getMonth() + 1) : '0' + (planDate.getMonth() + 1)
              this.formData.plan_month = planYear + '-' + planMonth
            }
            if (typeof this.formData.loading_date[0] === 'object') { // loading_date[0]日期转换
              let loading_date1 = new Date(this.formData.loading_date[0])
              let date1Year = loading_date1.getFullYear()
              let date1Month = (loading_date1.getMonth() + 1) > 9 ? (loading_date1.getMonth() + 1) : '0' + (loading_date1.getMonth() + 1)
              let date1Day = loading_date1.getDate() > 9 ? loading_date1.getDate() : '0' + loading_date1.getDate()
              this.formData.loading_date[0] = date1Year + '.' + date1Month + '.' + date1Day
            }
            if (typeof this.formData.loading_date[1] === 'object') { // loading_date[1]日期转换
              let loading_date2 = new Date(this.formData.loading_date[1])
              let date2Year = loading_date2.getFullYear()
              let date2Month = (loading_date2.getMonth() + 1) > 9 ? (loading_date2.getMonth() + 1) : '0' + (loading_date2.getMonth() + 1)
              let date2Day = loading_date2.getDate() > 9 ? loading_date2.getDate() : '0' + loading_date2.getDate()
              this.formData.loading_date[1] = date2Year + '.' + date2Month + '.' + date2Day
            }
            let isEqual = JSON.stringify(this.oldData) === JSON.stringify(this.formData)
            if (isEqual) { // 数据没有改变
              this.clearData()
              this.modalData.modal = false
            } else { // 数据改变了
              Object.assign(_param, {
                is_modify: 1
              })
              API.updateVmpPlan(_param).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.clearData()
                  this.$emit('planBack')
                  this.modalData.modal = false
                } else {
                  this.$Message.error(res.data.Message)
                }
              })
            }
          }
        }
      })
    },
    handleCancel () { // 取消
      this.modalData.modal = false
      this.clearData()
    },
    // 导入弹窗打开
    importPlanModal () {
      this.importModalData.modal = true
      this.importModalData.data = {
        ship_id: this.formData.ship_id
      }
    },
    // 导入回调
    importBackObj (obj) {
      this.clearData()
      setTimeout(() => { // 防止码头回显异常做个异步
        this.importModalData.modal = false
        if (typeof obj.plan_month === 'string') { // plan_month日期转换
          let planDate = new Date(obj.plan_month)
          let planYear = planDate.getFullYear()
          let planMonth = (planDate.getMonth() + 1) > 9 ? (planDate.getMonth() + 1) : '0' + (planDate.getMonth() + 1)
          this.formData.plan_month = planYear + '-' + planMonth
        } else {
          this.formData.plan_month = obj.plan_month
        }
        this.formData.ship_id = this.modalData.ship_id // 船舶id还是打开弹窗的默认id
        this.formData.loading_date = obj.loading_date
        this.formData.voyage_no = obj.voyage_no
        this.formData.load_port_id = obj.load_port_ids.split(',')
        this.formData.unload_port_id = obj.unload_port_ids.split(',')
        this.formData.load_port_ids = obj.load_port_ids
        this.formData.unload_port_ids = obj.unload_port_ids
        this.formData.load_wharf_id = obj.load_wharf_ids.split(',')
        this.formData.load_wharf_ids = obj.load_wharf_ids
        this.formData.unload_wharf_id = obj.unload_wharf_ids.split(',')
        this.formData.unload_wharf_ids = obj.unload_wharf_ids
        this.formData.goods_id = obj.goods_ids.split(',')
        this.formData.goods_amount = obj.goods_amount
        this.formData.load_require = obj.load_require
        this.formData.unload_require = obj.unload_require
        this.formData.recipient_company_id = obj.recipient_company_id
        this.formData.remarks = obj.remarks
      }, 100)
    },
    loadPortChange (item) { // 装港码头数据变化触发
      if (item.length > 0) {
        this.formData.load_port_ids = item.join()
        this.getWharfList('load')
      }
    },
    unloadPortChange (item) { // 卸港码头数据变化触发
      if (item.length > 0) {
        this.formData.unload_port_ids = item.join()
        this.getWharfList('unload')
      }
    },
    // 获取码头列表
    getWharfList (loadType) {
      let _param = loadType === 'load' ? { port_ids: this.formData.load_port_ids } : { port_ids: this.formData.unload_port_ids }
      basicAPI.queryBasicWharfList(_param).then(res => {
        if (res.data.Code === 10000) {
          if (loadType === 'load') {
            this.loadWharfList = res.data.Result
          } else {
            this.unloadWharfList = res.data.Result
          }
        }
      })
    },
    getBasicList () { // 获取基础数据
      // 获取港口数据
      basicAPI.queryBasicPortList().then(res => {
        if (res.data.Code === 10000) {
          this.portList = res.data.Result
        }
      })
      // 获取基础货品数据
      basicAPI.queryVmpBasicCargoList().then(res => {
        if (res.data.Code === 10000) {
          this.cargoList = res.data.Result
        }
      })
      // 获取客户信息
      basicAPI.queryUsCompanyList({ company_type: 2 }).then(res => { // 类型（1船东；2货主；3代理）
        if (res.data.Code === 10000) {
          this.companyList = res.data.Result
        }
      })
    },
    clearData () {
      this.formData = {
        plan_month: '',
        ship_id: '',
        loading_date: '',
        voyage_no: '',
        load_port_id: '',
        unload_port_id: '',
        load_wharf_id: '',
        unload_wharf_id: '',
        goods_id: '',
        goods_amount: '',
        load_require: '',
        unload_require: '',
        recipient_company_id: '',
        remarks: ''
      }
      this.loadWharfList = []
      this.unloadWharfList = []
      this.$refs['formInline'].resetFields()
    },
    modalShowHide (val) {
      if (val) {
        this.clearData()
        this.getBasicList()
        this.formData.ship_id = this.modalData.ship_id
        if (this.modalData.type === 'modify') {
          this.oldData = {
            vmp_plan_id: this.modalData.data.vmp_plan_id,
            plan_month: this.modalData.data.plan_month,
            ship_id: this.modalData.data.ship_id,
            loading_date: this.modalData.data.loading_date,
            voyage_no: this.modalData.data.voyage_no,
            load_port_id: this.modalData.data.load_port_ids.split(','),
            load_port_ids: this.modalData.data.load_port_ids,
            unload_port_id: this.modalData.data.unload_port_ids.split(','),
            unload_port_ids: this.modalData.data.unload_port_ids,
            load_wharf_id: this.modalData.data.load_wharf_ids.split(','),
            unload_wharf_id: this.modalData.data.unload_wharf_ids.split(','),
            load_wharf_ids: this.modalData.data.load_wharf_ids,
            unload_wharf_ids: this.modalData.data.unload_wharf_ids,
            goods_id: this.modalData.data.goods_ids.split(','),
            goods_amount: this.modalData.data.goods_amount,
            load_require: this.modalData.data.load_require,
            unload_require: this.modalData.data.unload_require,
            recipient_company_id: this.modalData.data.recipient_company_id,
            remarks: this.modalData.data.remarks
          }
          this.formData = {
            vmp_plan_id: this.modalData.data.vmp_plan_id,
            plan_month: this.modalData.data.plan_month,
            ship_id: this.modalData.data.ship_id,
            loading_date: this.modalData.data.loading_date,
            voyage_no: this.modalData.data.voyage_no,
            load_port_id: this.modalData.data.load_port_ids.split(','),
            load_port_ids: this.modalData.data.load_port_ids,
            unload_port_id: this.modalData.data.unload_port_ids.split(','),
            unload_port_ids: this.modalData.data.unload_port_ids,
            load_wharf_id: this.modalData.data.load_wharf_ids.split(','),
            unload_wharf_id: this.modalData.data.unload_wharf_ids.split(','),
            load_wharf_ids: this.modalData.data.load_wharf_ids,
            unload_wharf_ids: this.modalData.data.unload_wharf_ids,
            goods_id: this.modalData.data.goods_ids.split(','),
            goods_amount: this.modalData.data.goods_amount,
            load_require: this.modalData.data.load_require,
            unload_require: this.modalData.data.unload_require,
            recipient_company_id: this.modalData.data.recipient_company_id,
            remarks: this.modalData.data.remarks
          }
          // if (this.modalData.data.load_port_ids !== '') { // 装港码头列表
          //   this.getWharfList('load')
          // }
          // if (this.modalData.data.unload_port_ids !== '') { // 卸港码头列表
          //   this.getWharfList('unload')
          // }
          if (typeof this.modalData.data.plan_month === 'object' || typeof this.modalData.data.plan_month === 'string') { // plan_month日期转换
            let planDate = new Date(this.modalData.data.plan_month)
            let planYear = planDate.getFullYear()
            let planMonth = (planDate.getMonth() + 1) > 9 ? (planDate.getMonth() + 1) : '0' + (planDate.getMonth() + 1)
            this.oldData.plan_month = this.formData.plan_month = planYear + '-' + planMonth
          } else {
            this.oldData.plan_month = this.formData.plan_month = this.modalData.data.plan_month
          }
        }
      } else {
        this.clearData()
      }
    }
  }
}
</script>
<style scoped>
  .import_btn {
    position: absolute;
    right: 40px;
    top: 10px;
  }
</style>