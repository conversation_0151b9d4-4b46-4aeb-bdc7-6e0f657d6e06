import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

export const loginTest = ({ username, password, domainName }) => {
  const data = {
    username: username,
    password: password,
    domainName: domainName
  }
  let qsData = Qs.stringify(data)
  return axios.request({
    url: 'http://erp.xtshipping.net/pb/auth/api/loginEnhanced',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export const login = ({ userName, password }) => {
  const data = {
    username: userName,
    password: password
  }
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/sys/login/checkLogin',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 手机登录
export const phoneLogin = ({ userName }) => {
  const data = {
    username: userName
  }
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/sys/login/checkPhoneLogin',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export const getUserInfo = (token) => {
  return axios.request({
    url: 'get_info',
    params: {
      token
    },
    method: 'get'
  })
}

export const logout = (token) => {
  return axios.request({
    url: '/sys/login/loginOut',
    method: 'post'
  })
}

// 用户菜单权限
export function loginUserMenu (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/login/getUserMenu',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户按钮权限
export function loginUserSubMenu (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/login/getUserSubMenu',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 理发用户密码
export function updatePwd (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/alterUnifiedAccountPassword',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export const getUnreadCount = () => {
  return axios.request({
    url: 'message/count',
    method: 'get'
  })
}

export const getMessage = () => {
  return axios.request({
    url: 'message/init',
    method: 'get'
  })
}

export const getContentByMsgId = msg_id => {
  return axios.request({
    url: 'message/content',
    method: 'get',
    params: {
      msg_id
    }
  })
}

export const hasRead = msg_id => {
  return axios.request({
    url: 'message/has_read',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const removeReaded = msg_id => {
  return axios.request({
    url: 'message/remove_readed',
    method: 'post',
    data: {
      msg_id
    }
  })
}

export const restoreTrash = msg_id => {
  return axios.request({
    url: 'message/restore',
    method: 'post',
    data: {
      msg_id
    }
  })
}
