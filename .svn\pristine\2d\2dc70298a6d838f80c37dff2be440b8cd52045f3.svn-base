import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// InitFaceVerify 认证
export function initFaceVerify (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/aliyun/face/verify/initFaceVerify',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// describeFaceVerify 认证
export function describeFaceVerify (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/aliyun/face/verify/describeFaceVerify',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  initFaceVerify,
  describeFaceVerify
}