import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 模块管理列表 分页
export function querymodulePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/module/queryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 模块新增
export function addModuleData (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/module/add',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 模块编辑
export function updateModuleData (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/module/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}