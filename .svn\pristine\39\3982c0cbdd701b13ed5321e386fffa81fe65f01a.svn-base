<template>
  <div>
    <TEMP1 v-if="formType === '0'"></TEMP1>
    <TEMP2 v-if="formType === '1'"></TEMP2>
    <TEMP3 v-if="formType === '2'"></TEMP3>
  </div>
</template>
<script>
import TEMP1 from './performanceTemp/temp1.vue' // 通用模板
import TEMP2 from './performanceTemp/temp2.vue' // 业务模板
import TEMP3 from './performanceTemp/temp3.vue' // 市场部模板

export default ({
  components: {
    TEMP1,
    TEMP2,
    TEMP3
  },
  data () {
    return {
      formType: ''
    }
  },
  methods: {

  },
  created () {
    if (localStorage.getItem('formType')) { // 绩效档案存储模板类型
      this.formType = localStorage.getItem('formType')
    } else {
      let _flowObj = JSON.parse(localStorage.getItem('userFlow'))
      this.formType = _flowObj.form_num
    }
  },
  beforeDestroy () {
    // 移除便于判断是从绩效档案还是成员操作进来的formType传参
    localStorage.removeItem('formType')
  }
})
</script>
