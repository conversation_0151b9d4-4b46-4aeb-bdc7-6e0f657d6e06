<template>
  <Card>
    <Tabs @on-click="tabClick">
      <TabPane label="设备管理" name="tab1">
        <Row>
          <Col span="12">
            <Button type="primary" @click="handleEdit">新增设备</Button>
            <span class="but_on">
              <Button type="text" @click="submitSort">提交</Button>
              <Dropdown trigger="click">
                <Icon type="md-arrow-dropdown"></Icon>
                <Dropdown-menu slot="list">
                  <Dropdown-item>
                    <div @click="recallSort">撤销</div>
                  </Dropdown-item>
                </Dropdown-menu>
              </Dropdown>
            </span>
            <span class="but_on">
              <Button type="text" @click="auditSort">审核</Button>
              <Dropdown trigger="click">
                <Icon type="md-arrow-dropdown"></Icon>
                <Dropdown-menu slot="list">
                  <Dropdown-item>
                    <div @click="unAuditSort">反审核</div>
                  </Dropdown-item>
                </Dropdown-menu>
              </Dropdown>
            </span>
            <label style="margin: 0 5px 0 15px">数据状态</label>
            <Select v-model="status" multiple style="width:260px" @on-change="statusChange">
              <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </Col>
          <Col span="12" style="text-align: right;">
            <Dropdown @on-click="shipChange">
              <a href="javascript:void(0)" v-html="ship_name" style="color: #515a6e;"></a>
              <Icon type="md-arrow-dropdown"></Icon>
              <Dropdown-menu slot="list">
                <Dropdown-item v-for="(item, idx) in shipList" :name="item.name">{{item.name}}</Dropdown-item>
              </Dropdown-menu>
            </Dropdown>
            <Col span="12" style="text-align: right">
              <span style="line-height: 33px;">共{{total}}条结果</span>
              <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
            </Col>
          </Col>
        </Row>
        <Row>
          <Col span="4" style="border: 1px solid #ccc;">
            <h3 class="col-text">船舶设备</h3>
            <Tree :data="equipTree" :render="renderContent" class="tree" @on-toggle-expand="toggleExpand"></Tree>
          </Col>
          <Col span="20" style="padding-left: 10px;">
            <Table
              border
              ref="selection"
              :loading="loading"
              :columns="columns"
              :data="equipList"
              @on-row-click="tableClick"
              @on-select-all="tableSelectAll"
              @on-select-all-cancel="tableSelectCancel"
              @on-select="tableSelectAll"
              @on-select-cancel="tableSelectCancel"></Table>
            <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
            :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
          </Col>
        </Row>
      </TabPane>
      <TabPane label="设备分类" name="tab2">
        <Table border :loading="loading1" :columns="classiColumns" :data="classiList" maxHeight="500"></Table>
      </TabPane>
    </Tabs>
    <!-- 新增编辑弹窗内容 -->
    <Modal v-model="equipModal"
      :title="titleModal"
      @on-ok="handleSubmit('formInline')"
      @on-cancel="handleCancel"
      :width="modalType !== 'file' ? '70%' : '40%'"
      :mask-closable="false">
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="100">
        <div v-if="modalType !== 'file'" class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item label="设备分类">
                <Input type="text" v-model="formInline.name" readonly></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="父设备">
                <div v-if="formInline.equip_pr === ''">--</div>
                <div v-else>{{formInline.equip_pr}}</div>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="系统编码">
                <div>{{formInline.system_code}}</div>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="设备状态">
                <Radio-group v-model="formInline.state" type="button">
                  <Radio label="1" :disabled="formInline.status !== 'A'">启用</Radio>
                  <Radio label="2" :disabled="formInline.status !== 'A'">停用</Radio>
                </Radio-group>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="equip_name" label="设备名称">
                <Input type="text" v-model="formInline.equip_name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="设备型号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="classi" label="设备级别">
                <Select v-model="formInline.classi" placeholder="请选择">
                  <Option value="一般设备">一般设备</Option>
                  <Option value="安全设备">安全设备</Option>
                  <Option value="关键设备">关键设备</Option>
                  <Option value="应急设备">应急设备</Option>
                </Select>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="制造厂商">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="出厂编号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="生产日期">
                <Date-picker type="date" placeholder="请选择" v-model="formInline.date" style="width: 100%"></Date-picker>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="图纸编号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="证书号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Form-item label="技术参数">
              <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
            </Form-item>
          </Row>
        </div>
        <div class="tr-name" v-if="modalType !== 'file'">附件</div>
        <fileUpload ref="fileUploadComponent" :fileDataList="fileDataList" type="modalType" @getFileId="getFileId"></fileUpload>
      </Form>
    </Modal>
  </Card>
</template>
<script>
import search from '_c/search'
import fileUpload from '../../../performance/performanceTemp/fileUpload'
import { shipQuery, equipListLevelThree, equipListLevelMore } from '@/api/erpSys/equipManager'
import API from '@/api/erpSys/common'

export default {
  components: {
    search,
    fileUpload
  },
  data () {
    return {
      // 设备管理
      status: ['A', 'B', 'C'],
      statusList: [{
        value: 'A',
        label: '暂存'
      }, {
        value: 'B',
        label: '已提交'
      }, {
        value: 'C',
        label: '已审核'
      }], // 数据状态
      ship_name: '',
      shipList: [], // 储存船名下拉
      equipTreeSeled: {}, // 当前已选中的节点数组
      fileDataList: [], // 附件
      wps_ids: '',
      equipModal: false,
      titleModal: '',
      modalType: '',
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '设备名称',
          key: 'name',
          align: 'center'
        },
        {
          title: '设备型号',
          key: '',
          align: 'center'
        },
        {
          title: '制造厂商',
          key: '',
          align: 'center'
        },
        {
          title: '出厂编号',
          key: '',
          align: 'center'
        },
        {
          title: '图纸编号',
          key: '',
          align: 'center'
        },
        {
          title: '生产日期',
          key: 'date',
          align: 'center'
        },
        {
          title: '设备级别',
          key: 'level',
          align: 'center',
          filters: [
            {
              label: '一般设备',
              value: '一般'
            },
            {
              label: '安全设备',
              value: '安全'
            },
            {
              label: '关键设备',
              value: '关键'
            },
            {
              label: '应急设备',
              value: '应急'
            }
          ],
          filterMethod (value, row) {
            return row.level.indexOf(value) > -1
          }
        },
        {
          title: '设备状态',
          key: 'status',
          align: 'center',
          filters: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 2
            }
          ],
          filterMultiple: false,
          filterMethod (value, row) {
            if (value === 1) {
              return row.status === 1
            } else if (value === 2) {
              return row.status === 2
            }
          }
        },
        {
          title: '附件',
          key: '',
          align: 'center',
          width: 120,
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-link',
                size: 'small',
                type: 'text'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.modalType = 'file'
                  this.titleModal = '附件信息'
                  this.equipModal = true
                  this.handleUpload(params.row.id)
                }
              }
            }, // params.row.fileArr.length
            )
          }
        },
        {
          title: '操作',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-trash',
                size: 'small'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.handleDelete(params.row)                  
                }
              }
            })
          }
        }
      ],
      equipTree: [],
      selectedIds: [], // 已勾选数据
      selectionData: [],
      equipList: [], // 设备列表
      loading: false,
      total: 0,
      listQuery: {
        number: '03',
        createorg_number: 'xt',
        level: '2',
        pageSize: 10,
        pageNo: 1
      },
      listCurrent: 1,
      setSearchData: {
        name: {
          type: 'text',
          width: 180,
          value: '',
          isdisable: false,
          placeholder: '请输入设备名称、系统编码、设备型号、制造厂商'
        }
      },
      formInline: {},
      ruleInline: {
        name: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      // 设备分类
      loading1: false,
      classiQuery: {
        number: '02',
        createorg_number: 'xt',
        level: '2',
        status: ['A', 'B', 'C'],
        pageSize: 10000,
        pageNo: 1
      },
      classiList: [],
      classiColumns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '分类编码',
          key: 'number',
          width: 120,
          align: 'center'
        },
        {
          title: '分类名称',
          key: 'name',
          align: 'center',
          width: 300
        },
        {
          title: '适用船舶',
          key: 'xtgf_boatid',
          align: 'center',
          render: (h, params) => {
            let shipVal = []
            params.row.xtgf_boatid.forEach(e => {
              shipVal.push(e.name.toString() + '; ')
            })
            return h('span', shipVal)
          }
        }
      ]
    }
  },
  created () {
    shipQuery({ pageSize: 10000,pageNo: 1 }).then(res => { // 获取船舶列表
      if (res.status === 200) {
        this.shipList = res.data.data.rows
        this.ship_name = res.data.data.rows[0].name
        this.getEquipListLevelThree()
      }
    })
    this.getequipList()
  },
  methods: {
    // 获取船舶设备
    getEquipListLevelThree () {
      let data = {
        level: '3',
        name: this.ship_name,
        enable: '1',
        pageSize: 10000,
        pageNo: 1
      }
      equipListLevelThree(data).then(res => { // 船舶设备一级列表-通过船舶名称进行查询
        console.log(res)
        if (res.status === 200) {
          res.data.data.rows.forEach(e => {
            this.equipTree = [{
              title: e.parent_name,
              // expand: true,
              render: (h, { root, node, data }) => { // root根节点, node当前节点, data
                let equipListLevelFour = ''
                let equipListLevelFive = ''
                return h('span', {
                  style: {
                    display: 'inline-block',
                    width: '100%'
                  },
                  on: {
                    click: () => {
                      console.log(2)
                      // this.getequipList(data)
                      // this.equipTreeSeled.name = data.title
                      // if (!data.selected) {
                      //   this.getMaterialList(data)
                      //   this.curDataTree = data.number
                      // } else {
                      //   this.curDataTree = ''
                      //   this.getMaterialList()
                      // }
                    }
                  }
                }, [
                  h('span', [
                    h('Icon', {
                      props: {
                        type: 'ios-home-outline'
                      },
                      style: {
                        marginRight: '8px'
                      },
                      on: {
                        click: () => {
                          console.log(32)
                          // this.getequipList(data)
                          // this.equipTreeSeled.name = data.title
                          // if (!data.selected) {
                          //   this.getMaterialList(data)
                          //   this.curDataTree = data.number
                          // } else {
                          //   this.curDataTree = ''
                          //   this.getMaterialList()
                          // }
                        }
                      }
                    }),
                    h('span', data.title)
                  ])
                ])
              },
              children: [
                {
                  title: e.name,
                  children: [{
                    title: 'test111'
                  }]
                }
              ]
            }]
          })
        }
      })
    },
    toggleExpand(row) {
      console.log(row)
      if (row.nodeKey === 0 && row.expand) {
        equipListLevelMore({ number: e.number,level: '4' }).then(item => { // 获取设备二级
          console.log(item)
        })
      } else if (row.nodeKey === 1 && row.expand) {
        equipListLevelMore({ number: item.number,level: '5' }).then(val => { // 获取设备三级
          console.log(val)
        })
      }
    },
    // 获取设备列表
    getequipList (d) {
      // this.listQuery.classi_id = d
      this.loading = true
      // API.getEquipList(this.listQuery).then(res => {
      //   this.loading = false
      //   if (res.data.Code === 10000) {
      //     this.equipList = res.data.Result
      //     this.total = res.data.Total
      //   } else {
      //     // this.$Message.error(res.data.Message)
      //   }
      // })
    },
    // 新增设备
    handleEdit () {
      if (this.equipTreeSeled.name === undefined) {
        this.$Message.error('请先选择设备分类或设备')
      } else {
        this.titleModal = '新增设备信息'
        this.equipModal = true
        this.formInline.name = this.equipTreeSeled.name
        this.formInline.code = this.equipTreeSeled.code
      }
    },
    // 编辑设备信息
    tableClick (list) {
      this.equipModal = true
      this.titleModal = '编辑设备信息'
      this.formInline = list
      this.formInline.name = this.equipTreeSeled.name
      this.formInline.code = this.equipTreeSeled.code
      // this.fileDataList = list.files
    },
    // 保存
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认保存设备信息？</p>',
            loading: true,
            onOk: () => {
              API(this.formInline).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.$refs['name'].resetFields()
                  this.getequipList(this.equipTree[0].id)
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 关闭弹窗
    handleCancel () {
      this.modalType = ''
      this.equipModal = false
      this.$refs['formInline'].resetFields()
      this.formInline.date = ''
      this.formInline.state = '启用'
    },
    // 删除设备管理列表
    handleDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>删除后无法恢复，是否确认删除？</p>',
        loading: true,
        onOk: () => {
          // API.({ id: row.id }).then(res => {
          //   if (res.data.Code === 10000) {
          //     this.loading = false
          //     this.$Modal.remove()
          //     this.$Message.success(res.data.Message)
          //     this.getequipList(this.equipTree[0].id)
          //   } else {
          //     this.loading = false
          //     this.$Modal.remove()
          //     this.$Message.error(res.data.Message)
          //   }
          // })
        }
      })
    },
    // 附件上传 
    handleUpload (d) {},
    getFileId () {
      this.wps_ids = this.fileDataList.map(item => {
        return item.id
      }).join()
    },
    // 船名搜索
    shipChange (name) {
      this.ship_name = name
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getequipList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.question_topic.value = ''
      this.getequipList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getequipList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getequipList()
    },
    // 获取分类列表
    getClassiList () {
      this.loading1 = true
      API.getEquipList(this.classiQuery).then(res => {
        this.loading1 = false
        if (res.status === 200) {
          this.classiList = res.data.data.rows
        } else {}
      })
    },
    // 提交
    submitSort () {
      let idx = this.selectionData.findIndex(item => item.status !== 'A')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: ''
          }
          // API.transferStation(dataParam).then(res => {
          //   if (res.data.Result.status) {
          //     this.clearData()
          //     this.$Message.success('提交成功。')
          //     this.getequipList()
          //   }
          // })
        }
      } else {
        this.$Message.error('只有暂存的数据才允许提交。')
      }
    },
    // 撤销提交
    recallSort () {
      let idx = this.selectionData.findIndex(item => item.status !== 'B')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: ''
          }
          // API.transferStation(dataParam).then(res => {
          //   if (res.data.Result.status) {
          //     this.clearData()
          //     this.$Message.success('撤销成功。')
          //     this.getequipList()
          //   }
          // })
        }
      } else {
        this.$Message.error('只有已提交的数据才能撤销。')
      }
    },
    // 审核
    auditSort () {
      let idx = this.selectionData.findIndex(item => item.status !== 'B')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: ''
          }
          // API.transferStation(dataParam).then(res => {
          //   if (res.data.Result.status) {
          //     this.clearData()
          //     this.$Message.success('审核成功。')
          //     this.getequipList()
          //   }
          // })
        }
      } else {
        this.$Message.error('只有已提交的数据才能审核。')
      }
    },
    // 反审核
    unAuditSort () {
      let idx = this.selectionData.findIndex(item => item.status !== 'C')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: ''
          }
          // API.transferStation(dataParam).then(res => {
          //   if (res.data.Result.status) {
          //     this.clearData()
          //     this.$Message.success('反审核成功。')
          //     this.getequipList()
          //   }
          // })
        }
      } else {
        this.$Message.error('只有已审核的数据才能反审核。')
      }
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
      if (selection.length === 0) {
        this.selectedIds = []
      } else {
        this.selectedIds.forEach((e, idx) => {
          if (e === row.id) {
            this.selectedIds.splice(idx, 1)
          }
        })
      }
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.sortList.length) this.selectAll = true
      this.selectionData = selection
      if (row === undefined) {
        selection.map(e => {
          this.selectedIds.push(e.id)
        })
      } else {
        this.selectedIds.push(row.id)
      }
    },
    // 数据状态选择
    statusChange () {
      // this.listQuery.status = val.length === 0 ? ['A', 'B', 'C'] : val
      // if (this.setSearchData.name.value !== '') {
      //   this.searchResults(this.setSearchData.name.value)
      // } else {
      //   this.getMaterialList()
      // }
    },
    // tab切换
    tabClick (name) {
      if (name === 'tab1') {
        this.getequipList()
      } else {
        this.getClassiList()
      }
    },
    // 树节点
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
          click: () => {
                      console.log(12)
            // this.getequipList(data)
          }
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px'
            },
            on: {
              click: () => {
                          console.log(122)
                // this.getequipList(data)
              }
            }
          }),
          h('span', data.title)
        ])
      ])
    }
  }
}
</script>
<style lang="less" scoped>
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.search-vie {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
</style>
<style>
.tree.ivu-tree ul li {
  margin: 5px 0;
}
.tree .ivu-tree-title span {
  font-size: 16px;
  color: #666;
}
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
}
</style>
