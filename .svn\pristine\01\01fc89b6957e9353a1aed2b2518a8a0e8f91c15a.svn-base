<template>
  <div class="search_top">
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults="resetResults"></search>
      <Button type="primary" @click="exportData" class="price_btn">数据导出</Button>
    </Card>
    <Card style="margin-top: 10px;">
      <Row :gutter="16">
        <Col span="14">
          <ChartBar style="height: 300px;" :value="barQuantityData" text="区域加油量" unit="吨" :markLine="true"></ChartBar>
        </Col>
        <Col span="10">
          <Table border :loading="quantityLoading" ref="selection" :columns="quantityColumns" :data="quantityList"></Table>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 10px;">
      <Row :gutter="16">
        <Col span="14">
          <div class="seledate_bt">
            <label>对比时间：</label>
            <DatePicker type="month" v-model="queryParam.compare_month" @on-change="changeCompareMonth" :clearable="false" style="width: 100px"></DatePicker>
          </div>
          <Select v-model="selectedOilType" class="seleoil_bt" @on-change="changeOilType">
            <Option v-for="(item, index) in inventorynameList" :value="item.value" :key="index">{{ item.label }}</Option>
          </Select>
          <ChartBar style="height: 300px;" :value="barAvgPriceData" text="区域均价" unit="吨/元" :legendRight="140"></ChartBar>
        </Col>
        <Col span="10">
          <Table border :loading="avgPriceLoading" ref="selection" :columns="avgPriceColumns" :data="avgPriceList"></Table>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { ChartBar } from '_c/charts'
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryOilAreaList, exportOilAreaList } from '@/api/oilPrice'
export default {
  components: {
    ChartBar,
    search
  },
  data () {
    return {
      selectedOilType: '',
      inventorynameList: [],
      barQuantityData: {
        legend: ['重油', '轻油'],
        xAxis: [],
        data: [[], []]
      },
      barAvgPriceData: {
        legend: [],
        xAxis: [],
        data: [[], []]
      },
      queryParam: {
        compare_month: '',
        date_month: '',
        shipname: '',
        vendorname: ''
      },
      quantityLoading: false,
      avgPriceLoading: false,
      quantityList: [
        { inventoryname: '重油' },
        { inventoryname: '轻油' }
      ],
      quantityColumns: [
        {
          title: '油料(吨)',
          key: 'inventoryname',
          align: 'center',
          fixed: 'left',
          width: 100,
          render: (h, params) => {
            return h('div', {}, params.row.inventoryname)
          }
        }
      ],
      avgPriceList: [
        { tableLeftRow: '' },
        { tableLeftRow: '' },
        { tableLeftRow: '增长金额(吨/元)' },
        { tableLeftRow: '增长率(%)' }
      ],
      avgPriceColumns: [
        {
          title: '',
          key: 'tableLeftRow',
          align: 'center',
          fixed: 'left',
          width: 122,
          render: (h, params) => {
            params.column.title = this.selectedOilType
            return h('div', {}, params.row.tableLeftRow)
          }
        }
      ],
      setSearchData: {
        date_month: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      }
    }
  },
  created () {
    this.getData()
  },
  beforeDestroy() {
    this.$store.commit('setAreaAnalyse', this.queryParam)
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      this.clearData()
      queryOilAreaList(this.queryParam).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          // 区域加油量
          res.data.thisMonthObj.heavyOilResult.forEach((e, idx) => {
            if (idx < res.data.thisMonthObj.heavyOilResult.length - 2) {
              this.barQuantityData.xAxis.push(e.area_name)
              this.barQuantityData.data[0].push(e.area_cost_quantity)
              this.quantityColumns = [...this.quantityColumns, this.quantityColumns[idx + 1] = {
                title: e.area_name,
                key: 'area_cost_quantity' + idx,
                align: 'center',
                minWidth: 120
              }, {
                title: '百分比',
                key: 'area_cost_quantity_weight' + idx,
                align: 'center',
                minWidth: 120
              }]
              this.quantityList[0] = Object.assign(this.quantityList[0], {
                ['area_cost_quantity' + idx]: e.area_cost_quantity // + ' (' + e.area_cost_quantity_weight + '%)'
              }, {
                ['area_cost_quantity_weight' + idx]: e.area_cost_quantity_weight
              })
            }
          })
          res.data.thisMonthObj.lightOilResult.forEach((e, idx) => {
            if (idx < res.data.thisMonthObj.lightOilResult.length - 2) {
              this.barQuantityData.data[1].push(e.area_cost_quantity)
              this.quantityList[1] = Object.assign(this.quantityList[1], {
                ['area_cost_quantity' + idx]: e.area_cost_quantity // + ' (' + e.area_cost_quantity_weight + '%)'
              }, {
                ['area_cost_quantity_weight' + idx]: e.area_cost_quantity_weight
              })
            }
          })
          // 区域均价
          this.queryParam.compare_month = this.queryParam.compare_month === '' ? res.data.lastMonthObj.date_month : this.queryParam.compare_month
          if (typeof this.queryParam.compare_month === 'object') {
            let curYear = this.queryParam.compare_month.getFullYear()
            let curMonth = this.queryParam.compare_month.getMonth() + 1
            this.queryParam.compare_month = curYear + '-' + curMonth
          }
          this.barAvgPriceData.legend = [this.queryParam.compare_month, res.data.thisMonthObj.date_month]
          if (this.selectedOilType === '重油') {
            res.data.lastMonthObj.heavyOilResult.forEach((e, idx) => {
              this.barAvgPriceData.xAxis.push(e.area_name)
              this.barAvgPriceData.data[0].push(e.area_cost_avg)
              this.avgPriceList[0] = Object.assign(this.avgPriceList[0], {
                tableLeftRow: this.queryParam.compare_month + '均价(吨/元)',
                ['area_cost_avg' + idx]: e.area_cost_avg
              })
            })
            res.data.thisMonthObj.heavyOilResult.forEach((e, idx) => {
              this.barAvgPriceData.data[1].push(e.area_cost_avg)
              this.avgPriceList[1] = Object.assign(this.avgPriceList[1], {
                tableLeftRow: res.data.thisMonthObj.date_month + '均价(吨/元)',
                ['area_cost_avg' + idx]: e.area_cost_avg
              })
              this.avgPriceList[2] = Object.assign(this.avgPriceList[2], {
                ['area_cost_avg' + idx]: e.increase_avg_amount
              })
              this.avgPriceList[3] = Object.assign(this.avgPriceList[3], {
                ['area_cost_avg' + idx]: e.increase_avg_rate
              })
              this.avgPriceColumns = [...this.avgPriceColumns, this.avgPriceColumns[idx + 1] = {
                title: e.area_name,
                key: 'area_cost_avg' + idx,
                align: 'center',
                minWidth: 95,
                render: (h, params) => {
                  return h('div', {
                    style: {
                      color: params.index > 1 ? params.row['area_cost_avg' + idx] < 0 ? 'green' : 'red' : '#515a6e'
                    }
                  }, params.row['area_cost_avg' + idx])
                }
              }]
            })
          } else {
            res.data.lastMonthObj.lightOilResult.forEach((e, idx) => {
              this.barAvgPriceData.xAxis.push(e.area_name)
              this.barAvgPriceData.data[0].push(e.area_cost_avg)
              this.avgPriceList[0] = Object.assign(this.avgPriceList[0], {
                tableLeftRow: this.queryParam.compare_month + '均价(吨/元)',
                ['area_cost_avg' + idx]: e.area_cost_avg
              })
            })
            res.data.thisMonthObj.lightOilResult.forEach((e, idx) => {
              this.barAvgPriceData.data[1].push(e.area_cost_avg)
              this.avgPriceList[1] = Object.assign(this.avgPriceList[1], {
                tableLeftRow: res.data.thisMonthObj.date_month + '均价(吨/元)',
                ['area_cost_avg' + idx]: e.area_cost_avg
              })
              this.avgPriceList[2] = Object.assign(this.avgPriceList[2], {
                ['area_cost_avg' + idx]: e.increase_avg_amount
              })
              this.avgPriceList[3] = Object.assign(this.avgPriceList[3], {
                ['area_cost_avg' + idx]: e.increase_avg_rate
              })
              this.avgPriceColumns = [...this.avgPriceColumns, this.avgPriceColumns[idx + 1] = {
                title: e.area_name,
                key: 'area_cost_avg' + idx,
                align: 'center',
                minWidth: 95,
                render: (h, params) => {
                  return h('div', {
                    style: {
                      color: params.index > 1 ? params.row['area_cost_avg' + idx] < 0 ? 'green' : 'red' : '#515a6e'
                    }
                  }, params.row['area_cost_avg' + idx])
                }
              }]
            })
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 选择油料类型
    changeOilType (val) {
      this.getList()
    },
    // 选择对比时间
    changeCompareMonth (val) {
      this.queryParam.compare_month = val
      this.getList()
    },
    // 查询
    searchResults (e) {
      this.queryParam.shipname = e.shipname
      this.queryParam.vendorname = e.vendorname
      delete e.target
      if (typeof this.setSearchData.date_month.selected === 'object') {
        let curYear = this.setSearchData.date_month.selected.getFullYear()
        let curMonth = this.setSearchData.date_month.selected.getMonth() + 1
        this.setSearchData.date_month.selected = curYear + '-' + curMonth
      }
      this.queryParam.date_month = this.setSearchData.date_month.selected
      this.queryParam.compare_month = ''
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.setSearchData.date_month.selected = e.key
      }
    },
    // 重置数据
    clearData () {
      this.barQuantityData.xAxis = []
      this.barQuantityData.data = [[], []]
      this.barAvgPriceData.xAxis = []
      this.barAvgPriceData.data = [[], []]
      this.quantityColumns = [this.quantityColumns[0]]
      this.avgPriceColumns = [this.avgPriceColumns[0]]
    },
    // 重置
    resetResults () {
      this.queryParam = {
        compare_month: '',
        date_month: '',
        shipname: '',
        vendorname: ''
      }
      this.setSearchData.shipname.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.$store.state.setState.areaAnalyseParam = {}
      this.clearData()
      this.getSysDate()
    },
    // 数据导出
    exportData () {
      exportOilAreaList(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取系统时间
    getSysDate () {
      if (Object.keys(this.$store.state.setState.areaAnalyseParam).length > 0) {
        // 获取存储的入参
        let storeQuery = this.$store.state.setState.areaAnalyseParam
        this.setSearchData.date_month.selected = storeQuery.date_month
        this.queryParam.date_month = storeQuery.date_month
        this.getList()
      } else {
        API.getSysTime().then(res => {
          if (res.data.Code === 10000) {
            this.setSearchData.date_month.selected = res.data.now_date.substring(0, 7)
            this.queryParam.date_month = res.data.now_date.substring(0, 7)
            this.getList()
          }
        })
      }
    },
    async getData () {
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname
            })
          })
        }
      })
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      await API.materialInventoryname({ usefor: '油料' }).then(res => { // 获取名称
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.inventorynameList.push({
              value: item.inventoryname,
              label: item.inventoryname
            })
          })
          this.selectedOilType = res.data.Result[0].inventoryname
        }
      })
      this.getSysDate()
    }
  }
}
</script>
<style lang="less" scoped>
.price_btn {
  right: 10px;
  z-index: 1;
  position: absolute;
}
.seleoil_bt {
  width: 80px;
  float: right;
  z-index: 1;
  position: relative;
}
.seledate_bt {
  z-index: 1;
  position: relative;
  float: left;
  top: -3px;
  left: 110px;
}
</style>
<style lang="less">
.search_top {
  .filter-container {
    display: inline-block;
  }
}
</style>
