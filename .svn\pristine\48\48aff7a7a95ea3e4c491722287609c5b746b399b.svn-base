<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="300" @on-visible-change="modalShowHide" :mask-closable="false" ok-text="保存" @on-ok=
  "editHandle">
    <Form ref="formValide" :model="postData" :label-width="45" style="margin-bottom: 20px;">
      <FormItem label="职务">
        <span><Input type='text' v-model='postData.post_name'></Input></span>
      </FormItem>
    </Form>
  </Modal>
</template>
<script>
import API from '@/api/jurisdictionManage/departManagement'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      postData: {}
    }
  },
  methods: {
    // 编辑保存
    editHandle () {
      if (this.modalData.type === 'add') { // 新增
        API.addPostManagePage({
          dept_id: this.postData.dept_id,
          post_name: this.postData.post_name
        }).then(res => {
          this.$emit('postDataBack')
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.modalData.type === 'modify') { // 编辑
        API.updatePostManagePage({
          post_id: this.postData.post_id,
          dept_id: this.postData.dept_id,
          post_name: this.postData.post_name
        }).then(res => {
          this.$emit('postDataBack')
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 弹窗显示
    modalShowHide (val) {
      if (val) {
        this.postData = { ...{}, ...this.modalData.data }
      }
    }
  }
})
</script>
