<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="625" @on-visible-change="modalShowHide" :mask-closable="!modalData.type">
    <div v-if="modalData.title === '其他公司服务年限'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in modalData.data" :key="idx">
          <Form :label-width="65" style="margin-bottom: 20px;">
            <Row>
              <Col span="12">
                <FormItem label="职务">
                  <span>{{ item.crt_duty_name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="公司">
                  <span>{{ item.company_name }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="船舶">
                  <span>{{ item.ship_name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="时长(月)">
                  <span>{{ item.months || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="备注">
                <span>{{ item.bak || '-' }}</span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>

    <div v-if="modalData.title === '本公司服务年限'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in modalData.data" :key="idx">
          <Form :label-width="65" style="margin-bottom: 20px;">
            <Row>
              <Col span="12">
                <FormItem label="职务">
                  <span>{{ item.duty_name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="公司">
                  <span>本公司</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="船舶">
                  <span>{{ item.ship_name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="时长(月)">
                  <span>{{ item.service_months || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </div>

    <div v-if="modalData.title === '证书资质'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in modalData.data" :key="idx">
          <Form :label-width="85" style="margin-bottom: 20px;">
            <Row>
              <Col span="12">
                <FormItem label="证书名称">
                  <span>{{ item.certificate_name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="获取时间">
                  <span>{{ item.acquisition_time || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="有效期开始">
                  <span>{{ item.validity_start || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="有效期结束">
                  <span>{{ item.validity_end || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="备注">
                <span>{{ item.bak || '-' }}</span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>
    <!-- 往期航线详情 -->
    <div v-if="modalData.title === '往期航线详情'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in modalData.data" :key="idx">
          <Form :label-width="55" style="margin-bottom: 20px;">
            <Row>
              <Col span="8">
                <FormItem label="船舶">
                  <span>{{ item.ship_name }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="航次">
                  <span>{{ item.voyage_no || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="货品">
                  <span>{{ item.goods_name || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="装港">
                <span>{{ item.load_info || '-' }}</span>
              </FormItem>
            </Row>
            <Row>
              <FormItem label="卸港">
                <span>{{ item.unload_info || '-' }}</span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>
    <!-- 离靠码头详情 -->
    <div v-if="modalData.title === '离靠码头详情'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in modalData.data" :key="idx">
          <Form :label-width="55" style="margin-bottom: 20px;">
            <Row>
              <Col span="8">
                <FormItem label="船舶">
                  <span>{{ item.ship_name }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="航次">
                  <span>{{ item.voyage_no || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="时间">
                  <span>{{ item.voyage_over_date ?  item.voyage_over_date.split(' ')[0] : '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="港口">
                  <span>{{ item.port_name }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="码头">
                  <span>{{ item.wharf_name || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="状态">
                  <span>{{ item.port_type_name || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </div>
    <!-- 外部检查详情 -->
    <div v-if="modalData.title === '外部检查详情'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in modalData.data" :key="idx">
          <Form :label-width="65" style="margin-bottom: 20px;">
            <Row>
              <Col span="12">
                <FormItem label="船舶">
                  <span>{{ item.ship_name || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="名称">
                  <span>{{ item.inspection_name || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="时间">
                  <span>{{ item.inspection_time || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="评价">
                  <span>{{ item.evaluate_name || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="备注">
                <span>{{ item.bak || '-' }}</span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>
    <!-- 荣誉详情 -->
    <div v-if="modalData.title === '荣誉详情'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in modalData.data" :key="idx">
          <Form :label-width="55" style="margin-bottom: 20px;">
            <Row>
              <Col span="8">
                <FormItem label="船舶">
                  <span>{{ item.ship_name }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="航次">
                  <span>{{ item.voyage_no || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="时间">
                  <span>{{ item.get_time || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="标题">
                <span>{{ item.title_name || '-' }}</span>
              </FormItem>
            </Row>
            <Row>
              <FormItem label="备注">
                <span>{{ item.bak || '-' }}</span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {

    }
  },
  methods: {
    modalShowHide (val) {
      if (val) {
        // console.log(this.modalData)
      }
    }
  }
})
</script>
<style lang="less">
  .detail-con {
    margin-top: 20px;
    .detail-block {
      margin-top: 10px;
      .block-head {
        display: flex;
        height: 160px;
        background-color: #EFF8FF;
        border-top: 1px solid #DAE1ED;
        border-right: 1px solid #DAE1ED;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
      .detail-other-block {
        font-size: 12px;
        font-weight: 500;
        color: #4A4A4A;
        margin-bottom: 10px;
      }
    }
    .ivu-form {
      border-left: 1px solid #DAE1ED;
      border-bottom: 1px solid #DAE1ED;
    }
    .detail-port-type {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }
    .detail-name {
      font-size: 16px;
      color: #333;
      font-weight: 600;
      margin-left: 20px;
    }
    .detail-time {
      font-size: 16px;
      color: #333;
      font-weight: 400;
      margin-left: 20px;
    }
    .ivu-form-item {
      margin-bottom: 0;
      height: 40px;
      line-height: 40px;
    }
    .ivu-form-item-label {
      padding: 0 10px;
      text-align: left;
      font-weight: 600;
      border: 1px solid #DAE1ED;
      border-bottom: none;
      border-left: none;
      background-color: #EFF8FF;
      height: 40px;
      line-height: 40px;
    }
    .ivu-form-item-content {
      border: 1px solid #DAE1ED;
      border-left: none;
      border-bottom: none;
      padding: 0 10px;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>