<template>
  <div class="main_area">
    <div class="menu_area">
      <div class="main_menu" @click="goto('materialSinglePrice')">
        <img :src="home1" alt="">
      </div>
      <div v-if="isLocalIp" class="main_menu" @click="goto('shipperOwner')">
        <img :src="home2" alt="">
      </div>
      <div v-if="isLocalIp" class="main_menu" @click="erpLogin">
        <img :src="home3" alt="">
      </div>
      <div v-if="isLocalIp" class="main_menu" @click="hcLogin">
        <img :src="home4" alt="">
      </div>
      <div v-if="isLocalIp" class="main_menu" @click="qglcLogin">
        <img :src="home5" alt="">
      </div>
      <div v-if="isExam" class="main_menu" @click="goto('memberManagement')">
        <img :src="home6" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import home1 from '@/assets/images/cgsjfx.png'
import home2 from '@/assets/images/cydsj.png'
import home3 from '@/assets/images/cbglpt.png'
import home4 from '@/assets/images/hcdt.png'
import home5 from '@/assets/images/qglctj.png'
import home6 from '@/assets/images/exam.png'

export default {
  name: 'home',
  data () {
    return {
      home1,
      home2,
      home3,
      home4,
      home5,
      home6
    }
  },
  computed: {
    isLocalIp () {
      console.log(localStorage.getItem('userAuth'))
      let usrIp = sessionStorage.getItem('ip')
      if (usrIp === '**************') return true
      return false
    },
    isExam () {
      if (localStorage.getItem('userDataId') === 'a5f4cc75010e44f4b8ec175b1db284ff') return true
      return false
    }
  },
  methods: {
    // 路由跳转
    goto (str) {
      switch (str) {
        case 'memberManagement':
          this.$store.commit('setAccess', 'examSystem')
          break
        case 'materialSinglePrice':
          this.$store.commit('setAccess', 'materialPurchase')
          break
        case 'shipperOwner':
          this.$store.commit('setAccess', 'shipperOwner')
          break
        default:
          break
      }
      this.$nextTick(() => {
        this.$router.push({
          name: str
        })
      })
    },
    // erp系统
    erpLogin () {
      let _uuid = localStorage.getItem('uuid')
      window.open('http://erp.xtshipping.net/#/login?' + _uuid, '_blank')
    },
    // 航次动态
    hcLogin () {
      window.open('http://hc.xtshipping.net', '_blank')
    },
    // 全国炼厂
    qglcLogin () {
      window.open('http://************:8080', '_blank')
    }
  }
}
</script>

<style lang="less">
  .main_area {
    display: flex;
    width: 100%;
    height: 100%;
    background-image: url('../../../assets/images/login-bg.png');
    background-size: cover;
    background-position: bottom;
    position: relative;
    align-items: center;
  }
  .main_menu {
    position: relative;
    text-align: center;
    cursor: pointer;
  }
  .menu_area {
    display: flex;
    // padding: 16% 0;
    flex-flow: wrap;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }
</style>
