<style lang="less">
  @import './login.less';
</style>

<template>
  <div class="login">
    <div class="login-head">
      <img class="head-logo" :src="logo" alt="">
      <span class="head-text">
        <span class="heead-text-cnname">兴通海运股份有限公司</span>
        <span class="heead-text-enname">XINGTONG SHIPPING CO., LTD.</span>
      </span>
      <span class="head-point">|</span>
      <span class="head-text">一体化管理平台</span>
    </div>
    <div class="login-search" v-if="this.$route.name === 'login'">
      <div class="search-title">国际一流</div>
      <div class="search-title">国内领先的</div>
      <div class="search-title">化工供应链综合服务商</div>
    </div>
    <div class="login-area">
      <div class="login-con">
        <div class="login-title">
          <div>登录</div>
        </div>
        <!-- <Card icon="log-in" title="欢迎登录" :bordered="false"> -->
        <div class="form-con">
          <login-form @on-success-valid="handleSubmit"></login-form>
        </div>
        <p slot="footer"></p>
        <!-- </Card> -->
      </div>
    </div>
    <div class="login-footer">
      <div class="footer-text">
        <div>地址：福建省泉州市泉港区驿峰东路295号兴通海运大厦8-9楼</div>
        <div>Copyright © 2019 兴通海运股份有限公司 保留所有版权 闽ICP备15001600号-3</div>
      </div>
    </div>
  </div>
</template>

<script>
import LoginForm from '_c/login-form'
import { mapActions } from 'vuex'
import logo from '@/assets/images/logo-boat.png'
import loginTip from '@/assets/images/login-tip.png'
export default {
  components: {
    LoginForm
  },
  data () {
    return {
      logo,
      loginTip
    }
  },
  methods: {
    ...mapActions([
      'handleLogin',
      'handlePhoneLogin'
    ]),
    getUrlKey (name) {
      // eslint-disable-next-line no-sparse-arrays
      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')) || null
    },
    handleSubmit ({ userName, password, way }) {
      this.handleLogin({ userName, password, way }).then(res => {
        if (res.Code === 10000) {
          this.$router.push({
            name: this.$config.homeName
          })
        } else {
          this.$Message.error(res.Message)
        }
      })
    }
  },
  created () {
    let _user = this.getUrlKey('user')
    let router = this.getUrlKey('router')
    if (_user && (document.referrer.includes('ierptest.xtshipping.com') || document.referrer.includes('erp.xtshipping.com'))) {
      this.handlePhoneLogin({ 'userName': _user }).then(res => {
        if (res.Code === 10000) {
          if (router) {
            if (router.indexOf('/') > 0) {
              this.$router.push({
                name: router.split('/')[0],
                params: {
                  id: router.split('/')[1]
                }
              })
            } else {
              if (router === 'erp') { // 跳转至瀛海登录
                let _uuid = localStorage.getItem('uuid')
                window.open('http://erp.xtshipping.net/#/login?' + _uuid + '&resourceflag=1', '_self')
              } else {
                this.$router.push({
                  name: router
                })
              }
            }
          } else {
            this.$router.push({
              name: this.$config.homeName
            })
          }
        } else {
          this.$Message.error(res.Message)
        }
      })
    }
  }
}
</script>
