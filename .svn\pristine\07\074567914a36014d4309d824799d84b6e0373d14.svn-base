<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px;position: relative;">
      <Button type="primary" @click="priceChange" class="price_btn">卓创价格</Button>
      <chart-line style="height: 300px;" unit="吨/元" :value="lineData" :formatter="true" @clickBack="linkBack" :clickable="true" :color="lineColor" :showOnemarkLine="true" rotate="45" :text='lineText'/>
      <h3 class="text_con">
        油料采购数据表
        <Button type="text" @click="exportData">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
      </h3>
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.queryParam.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <!-- 卓创价格弹窗内容 -->
    <Drawer title="卓创价格变化" v-model="priceDrawer" width="950">
      <Button type="primary" @click="exportOilPriceData" style="float: right;">数据导出</Button>
      <search @searchResults='searchResultsDrawer' @selectOnChanged='selectOnChangedDrawer' :setSearch='setSearchDataDrawer' @resetResults='resetResultsDrawer'></search>
      <chart-line v-if="priceDrawer" style="height: 300px;margin-top: 20px;" unit="吨/元" :value="priceLineData" :color="priceLineColor" rotate="45" text='每日价格走势'/>
    </Drawer>
    <!-- 备注弹窗内容 -->
    <Modal v-model="remarkModal" title="备注" width="320">
      <div class="modal_content">
        <div>
          <label>日期：</label>{{ modalParam.linkXAxisData }}
        </div>
        <div>
          <label>备注：</label>
          <Input type="textarea" v-model="modalParam.remark"></Input>
        </div>
      </div>
      <div slot="footer">
        <Button type="primary" @click="remarkModal = false">取消</Button>
        <Button type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { ChartLine } from '_c/charts'
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryAreaList } from '@/api/areaConfigure'
import { queryOilListByDayPage, queryOilListByDayList, exportOilListByDay } from '@/api/oilPrice'
import { oilPriceListForType, exportOilPriceList } from '@/api/trilateralOilPrice'
export default {
  components: {
    ChartLine,
    search
  },
  data () {
    return {
      lineText: '',
      queryParam: {
        date_month_st: '',
        date_month_et: '',
        shipname: '',
        vendorname: '',
        inventoryname: '',
        unified_area_id: '',
        pageSize: 10,
        pageIndex: 1
      },
      lineData: {
        xAxis: [],
        legend: ['', ''],
        smooth: 0,
        symbol: ['emptyCircle', 'none'],
        data: [[], []]
      },
      lineColor: ['#6699FF', '#E74823'],
      remarkModal: false,
      modalParam: {
        linkXAxisData: '',
        remark: '',
        code: '', // 入库单号
        inventorycode: '' // 存货编码
      },
      loading: false,
      list: [],
      lineDatalist: [],
      total: 0,
      listCurrent: 1,
      columns: [
        {
          title: '入库时间',
          key: 'date',
          align: 'center',
          width: 100
        },
        {
          title: '订单时间',
          key: 'date',
          align: 'center',
          width: 100
        },
        {
          title: '船舶',
          key: 'shipname',
          align: 'center',
          width: 110
        },
        {
          title: '',
          key: 'price',
          align: 'center',
          render: (h, params) => {
            params.column.title = this.queryParam.inventoryname + '价格（吨/元）'
            return h('div', {}, params.row.price)
          }
        },
        {
          title: '',
          key: '',
          align: 'center',
          render: (h, params) => {
            params.column.title = '三方' + this.queryParam.inventoryname + '价格区间（吨/元）'
            return h('div', {}, params.row.third_bottom_price + '~' + params.row.third_top_price)
          }
        },
        {
          title: '',
          key: 'third_middle_price',
          align: 'center',
          render: (h, params) => {
            params.column.title = '三方' + this.queryParam.inventoryname + '均价（吨/元）'
            return h('div', {}, params.row.third_middle_price)
          }
        },
        {
          title: '采购量（吨）',
          key: 'quantity',
          align: 'center'
        },
        {
          title: '',
          key: 'iSum',
          align: 'center',
          render: (h, params) => {
            params.column.title = this.queryParam.inventoryname + '总金额（元）'
            return h('div', {}, params.row.iSum)
          }
        },
        {
          title: '区域',
          key: 'area_name',
          align: 'center',
          render: (h, params) => {
            return h('Select', {
              props: {
                'label-in-value': true,
                value: params.row.unified_area_id
              },
              on: {
                'on-change': (val) => {
                  if (val === undefined) return
                  let data = {
                    code: params.row.code,
                    inventorycode: params.row.inventorycode,
                    unified_area_id: val.value,
                    area_name: val.label
                  }
                  if (params.row.area_name !== '' && params.row.unified_area_id !== '') {
                    this.$Modal.confirm({
                      title: '提示',
                      content: '<p>将' + params.row.date + params.row.inventoryname + '采购区域调整为：' + val.label + '</p>',
                      loading: true,
                      onOk: () => {
                        API.addOrUpdateAffiliate(data).then(res => {
                          if (res.data.Code === 10000) {
                            this.loading = false
                            this.$Modal.remove()
                            this.remarkModal = false
                            this.$Message.success(res.data.Message)
                            this.getListData()
                          } else {
                            this.loading = false
                            this.$Modal.remove()
                            this.$Message.error(res.data.Message)
                          }
                        })
                      },
                      onCancel: () => {
                        this.getListData()
                      }
                    })
                  } else {
                    API.addOrUpdateAffiliate(data).then(res => {
                      if (res.data.Code === 10000) {
                        this.$Message.success(res.data.Message)
                        // this.getListData()
                      } else {
                        this.$Message.error(res.data.Message)
                      }
                    })
                  }
                }
              }
            },
            this.setSearchData.unified_area_id.selectData.map(item => {
              return h('Option', {
                  props: {
                    value: item.value,
                    label: item.label
                  }
                })
              })
            )
          }
        },
        {
          title: '供应商',
          key: 'vendorname',
          align: 'center'
        }
      ],
      setSearchData: {
        date_month_st: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        date_month_et: {
          type: 'month_end',
          label: '-',
          selected: '',
          width: 100,
          value: '',
          isdisabled: false
        },
        inventoryname: {
          type: 'select',
          label: '名称',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true,
          isdisabled: false
        },
        unified_area_id: {
          type: 'select',
          label: '区域',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true,
          isdisabled: false
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      },
      // 卓创价格部分
      priceDrawer: false,
      nowDateSt: '',
      nowDateEt: '',
      priceLineColor: ['#60d937', '#2697d8'],
      priceLineData: {
        xAxis: [],
        legend: ['重油', '轻油'],
        smooth: 0,
        symbol: ['none', 'none'],
        data: [[], []]
      },
      setSearchDataDrawer: {
        release_date_st: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        release_date_et: {
          type: 'month_end',
          label: '-',
          selected: '',
          width: 100,
          value: '',
          isdisabled: false
        }
      }
    }
  },
  created () {
    this.getSysDate()
    this.getData()
  },
  beforeDestroy() {
    this.$store.commit('setDayAnalyse', this.queryParam)
  },
  methods: {
    // 获取列表
    getList () {
      this.lineData.xAxis = []
      this.lineData.data = [[], []]
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      queryOilListByDayList(this.queryParam).then(res => { // 获取无分页统计图
        if (res.data.Code === 10000) {
          this.lineDatalist = res.data.Result
          this.lineText = this.queryParam.inventoryname + '价格对比'
          this.lineData.legend = [this.queryParam.inventoryname, '三方' + this.queryParam.inventoryname]
          res.data.Result.forEach(e => {
            this.lineData.xAxis.push(e.date + ',' + e.remark)
            this.lineData.data[0].push(e.price)
            this.lineData.data[1].push(e.third_middle_price + ',' + e.third_bottom_price + '~' + e.third_top_price)
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取分页列表
    getListData () {
      this.loading = true
      this.list = []
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      queryOilListByDayPage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 统计图点击回调
    linkBack (val) {
      if (val.name === '平均值' || val.componentIndex === 1) return
      this.remarkModal = true
      this.modalParam.remark = this.lineDatalist[val.dataIndex].remark
      this.modalParam.linkXAxisData = val.name
      this.modalParam.code = this.lineDatalist[val.dataIndex].code
      this.modalParam.inventorycode = this.lineDatalist[val.dataIndex].inventorycode
    },
    // 备注新增/修改保存
    updateData () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认保存备注内容吗？</p>',
        loading: true,
        onOk: () => {
          API.addOrUpdateAffiliate(this.modalParam).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.remarkModal = false
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.queryParam.pageIndex = 1
      this.queryParam.shipname = e.shipname
      this.queryParam.vendorname = e.vendorname
      this.queryParam.inventoryname = e.inventoryname
      this.queryParam.unified_area_id = e.unified_area_id
      delete e.target
      this.getList()
      this.getListData()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.date_month_st = e.key
      } else if (e.flag === 'month_end') {
        this.date_month_et = e.key
      }
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.queryParam.shipname = ''
      this.queryParam.vendorname = ''
      this.queryParam.unified_area_id = ''
      this.setSearchData.shipname.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.setSearchData.unified_area_id.selected = ''
      this.setSearchData.inventoryname.selected = this.setSearchData.inventoryname.selectData[1].value
      this.queryParam.inventoryname = this.setSearchData.inventoryname.selectData[1].value
      this.$store.state.setState.dayAnalyseParam = {}
      this.getSysDate()
      this.getList()
      this.getListData()
    },
    // 数据导出
    exportData () {
      exportOilListByDay(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 卓创价格弹窗开启
    priceChange () {
      this.priceDrawer = true
      this.setSearchDataDrawer.release_date_st.selected = this.nowDateSt
      this.setSearchDataDrawer.release_date_et.selected = this.nowDateEt
      this.getOilPriceList()
    },
    // 卓创价格列表
    getOilPriceList () {
      this.priceLineData.xAxis = []
      this.priceLineData.data = [[], []]
      let data = {
        release_date_st: this.setSearchDataDrawer.release_date_st.selected,
        release_date_ed: this.setSearchDataDrawer.release_date_et.selected
      }
      oilPriceListForType(data).then(res => {
        if (res.data.Code === 10000) {
          res.data.heavyOilArr.forEach(e => {
            this.priceLineData.xAxis.push(e.release_date)
            this.priceLineData.data[0].push(e.middle_price)
          })
          res.data.lightOilArr.forEach(e => {
            this.priceLineData.data[1].push(e.middle_price)
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 卓创价格弹窗 查询
    searchResultsDrawer () {
      this.setSearchDataDrawer.release_date_st.selected = this.release_date_st
      this.setSearchDataDrawer.release_date_et.selected = this.release_date_ed
      this.getOilPriceList()
    },
    selectOnChangedDrawer (e) {
      if (e.flag === 'month_start') {
        this.release_date_st = e.key
      } else if (e.flag === 'month_end') {
        this.release_date_ed = e.key
      }
    },
    // 卓创价格 数据导出
    exportOilPriceData () {
      let data = {
        release_date_st: this.setSearchDataDrawer.release_date_st.selected,
        release_date_ed: this.setSearchDataDrawer.release_date_et.selected
      }
      exportOilPriceList(data).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 卓创弹窗 重置
    resetResultsDrawer () {
      this.getSysDate()
      this.setSearchDataDrawer.release_date_st.selected = this.nowDateSt
      this.setSearchDataDrawer.release_date_et.selected = this.nowDateEt
      this.getOilPriceList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.queryParam.pageSize = val
      this.getListData()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.queryParam.pageIndex = val
      this.$forceUpdate()
      this.getListData()
    },
    // 获取系统时间
    getSysDate () {
      if (Object.keys(this.$store.state.setState.dayAnalyseParam).length > 0) {
        // 获取存储的入参
        let storeQuery = this.$store.state.setState.dayAnalyseParam
        this.setSearchData.date_month_st.selected = storeQuery.date_month_st
        this.setSearchData.date_month_et.selected = storeQuery.date_month_et
        this.date_month_st = storeQuery.date_month_st
        this.date_month_et = storeQuery.date_month_et
      } else {
        API.getSysTime().then(res => {
          if (res.data.Code === 10000) {
            this.setSearchData.date_month_st.selected = res.data.now_date.substring(0, 5) + '01'
            this.setSearchData.date_month_et.selected = res.data.now_date.substring(0, 7)
            this.date_month_st = res.data.now_date.substring(0, 5) + '01'
            this.date_month_et = res.data.now_date.substring(0, 7)
            this.nowDateSt = res.data.now_date.substring(0, 5) + '01'
            this.nowDateEt = res.data.now_date.substring(0, 7)
          }
        })
      }
    },
    getData () {
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname
            })
          })
        }
      })
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      // 获取区域
      queryAreaList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.unified_area_id.selectData.push({
              value: item.unified_area_id,
              label: item.area_name
            })
          })
        }
      })
      API.materialInventoryname({ usefor: '油料' }).then(res => { // 获取名称
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.inventoryname.selectData.push({
              value: item.inventoryname,
              label: item.inventoryname
            })
          })
          this.queryParam.inventoryname = res.data.Result[1].inventoryname
          this.setSearchData.inventoryname.selected = res.data.Result[1].inventoryname
          this.getList()
          this.getListData()
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.price_btn {
  right: 10px;
  z-index: 1;
  position: absolute;
}
.text_con {
  margin: 10px 0;
  button {
    float: right;
    color: #007DFF;
    font-size: 14px;
    font-weight: bold;
    margin-top: -5px;
    .ivu-icon {
      font-weight: bold;
    }
  }
}
.modal_content {
  font-size: 16px;
  > div:first-child {
    margin-bottom: 10px;
  }
  label {
    vertical-align: top;
  }
  .ivu-input-wrapper {
    width: 80%;
  }
}
</style>
