<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="train_info">
        <h3>培训信息</h3>
        <p>
          <span><img src="../../../../assets/images/theme.png" alt="" style="width: 12px;">课题数量 &nbsp;&nbsp; </span>
          <span style="font-size: 32px">{{ theme_num }}</span>个
          <span style="margin-left: 150px"><img src="../../../../assets/images/train.png" alt="" style="width: 14px;">培训时长 &nbsp;&nbsp; </span>
          <span style="font-size: 32px">{{ total_time_sum }}</span>分钟
        </p>
      </div>
    </Card>
    <Card style="margin-top: 10px" class="train_info">
      <h3>排行榜前十</h3>
      <Row style="line-height:33px">
        <Col span="7">
          <h4 class="table_lbody">培训总时长/签到次数<Button type="text" @click="moreList(list.durationStat, 'train')">更多</Button></h4>
          <Row style="color: #1F2633">
            <Col span="1">&nbsp;</Col>
            <Col span="9">姓名</Col>
            <Col span="9">时长</Col>
            <Col span="5" style="text-align: center">次数</Col>
          </Row>
          <Row v-for="(item, idx) in list.durationStat" :key="idx">
            <Col span="1" :class="{'first_class': idx === 0, 'second_class': idx === 1, 'third_class': idx === 2}">{{ idx+1 }}</Col>
            <Col span="9">{{ item.member_name }}</Col>
            <Col span="9">{{ item.member_train_duration }}分钟</Col>
            <Col span="5" style="text-align: center">{{ item.member_sign_times }}次</Col>
          </Row>
        </Col>
        <Col span="7" offset="1">
          <h4 class="table_lbody">考试成绩/次数<Button type="text" @click="moreList(list.memberExamStat, 'exam')">更多</Button></h4>
          <Row style="color: #1F2633">
            <Col span="1">&nbsp;</Col>
            <Col span="9">姓名</Col>
            <Col span="9">成绩</Col>
            <Col span="5" style="text-align: center">次数</Col>
          </Row>
          <Row v-for="(item, idx) in list.memberExamStat" :key="idx">
            <Col span="1" :class="{'first_class': idx === 0, 'second_class': idx === 1, 'third_class': idx === 2}">{{ idx+1 }}</Col>
            <Col span="9">{{ item.member_name }}</Col>
            <Col span="9">{{ item.member_avg_score }}分</Col>
            <Col span="5" style="text-align: center">{{ item.member_exam_times }}次</Col>
          </Row>
        </Col>
        <Col span="7" offset="1">
          <h4 class="table_lbody">讲师评分/参与人数<Button type="text" @click="moreList(list.lecturerStat, 'evaluate')">更多</Button></h4>
          <Row style="color: #1F2633">
            <Col span="1">&nbsp;</Col>
            <Col span="9">姓名</Col>
            <Col span="9">评分</Col>
            <Col span="5" style="text-align: center">人数</Col>
          </Row>
          <Row v-for="(item, idx) in list.lecturerStat" :key="idx">
            <Col span="1" :class="{'first_class': idx === 0, 'second_class': idx === 1, 'third_class': idx === 2}">{{ idx+1 }}</Col>
            <Col span="9">{{ item.train_lecturer }}</Col>
            <Col span="9">{{ item.theme_evaluate_score }}分</Col>
            <Col span="5" style="text-align: center">{{ item.train_member_num }}人</Col>
          </Row>
        </Col>
      </Row>
      <detailModal :modalData="modalData"></detailModal>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import detailModal from './detailModal'
import { queryTrainLecturerStatPage } from '@/api/examSystem/trainingModule/memberStatistics'
export default {
  components: {
    search,
    detailModal
  },
  data () {
    return {
      theme_num: '',
      total_time_sum: '',
      list: [],
      loading: false,
      listQuery: {
        train_date_start: '',
        train_date_end: ''
      },
      setSearchData: {
        train_date: {
          type: 'date_range',
          label: '时间',
          selected: [],
          value: [],
          width: 190,
          isdisable: false
        }
      },
      modalData: {
        modal: false,
        type: '',
        title: '',
        data: undefined
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      queryTrainLecturerStatPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data
          this.theme_num = res.data.theme_num
          this.total_time_sum = res.data.total_time_sum
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 时间格式
    selectOnChanged (e) {
      this.listQuery.train_date_start = e.key[0]
      this.listQuery.train_date_end = e.key[1]
    },
    // 查询
    searchResults (e) {
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.train_date_start = ''
      this.listQuery.train_date_end = ''
      this.getList()
    },
    // 开启更多弹窗
    moreList (row, type) {
      this.modalData = {
        data: row,
        modal: true,
        type: type
      }
      if (type === 'train') {
        this.modalData.title = '培训总时长/签到次数'
      } else if (type === 'exam') {
        this.modalData.title = '考试成绩/次数'
      } else {
        this.modalData.title = '讲师评分/参与人数'
      }
    }
  }
}
</script>
<style lang="less" scoped>
.train_info {
  margin: 20px 0 15px;
  h3 {
    margin-bottom: 20px;
    color: #000;
    font-size: 18px;
  }
  span {
    color: #333;
    font-size: 14px;
  }
  span > img {
    margin-right: 5px;
    vertical-align: text-top;
  }
}
.table_lbody {
  color: #1F2633;
  font-size: 16px;
  margin-bottom: 10px;
  button {
    color: #3D7FFF;
    font-size: 14px;
    font-weight: 600;
    float: right;
    padding: 0;
  }
}
.first_class {
  color: #FF3B3B;
}
.second_class {
  color: #F5A623;
}
.third_class {
  color: #3D7FFF;
}
</style>