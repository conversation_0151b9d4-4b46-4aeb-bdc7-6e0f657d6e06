<template>
  <div>
    <Card style="margin-top: 10px">
      <Button class="add-part-btn" type="primary" @click="addPartModal">添加部门</Button>
      <Table class="table-list" border :loading="loading" ref="selection" :columns="columns" :data="departList"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <AddPart :modalData="addModalObj" @modalDataBack="getList"></AddPart>
    <PostModal :modalData="postModalObj"></PostModal>
    <UserModal :modalData="userModalObj"></UserModal>
    <ConfigModal :modalData="configModalObj"></ConfigModal>
    <BiConfigModal :modalData="biModalObj"></BiConfigModal>
  </div>
</template>
<script>
import API from '@/api/jurisdictionManage/departManagement'
import AddPart from './Modal/addPartModal.vue'
import PostModal from './Modal/postModal.vue'
import UserModal from './Modal/userModal.vue'
import ConfigModal from './Modal/configModal.vue'
import BiConfigModal from './Modal/biConfigModal.vue'

export default ({
  components: {
    AddPart,
    PostModal,
    UserModal,
    ConfigModal,
    BiConfigModal
  },
  data () {
    return {
      loading: false,
      total: 0,
      departList: [],
      listCurrent: 1,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      addModalObj: {
        title: '添加部门',
        type: '',
        modal: false,
        data: {}
      },
      postModalObj: {
        modal: false,
        title: '',
        data: {}
      },
      userModalObj: {
        modal: false,
        title: '',
        data: {}
      },
      configModalObj: {
        modal: false,
        title: '',
        data: {}
      },
      biModalObj: {
        modal: false,
        title: '',
        data: {}
      },
      columns: [
        {
          title: '部门',
          key: 'dept_name',
          align: 'center'
        },
        {
          title: '分管领导',
          key: 'leader_name',
          align: 'center'
        },
        {
          title: '手机号码',
          key: 'leader_mobile',
          align: 'center'
        },
        {
          title: '负责人',
          key: 'charge_name',
          align: 'center'
        },
        {
          title: '手机号码',
          key: 'charge_mobile',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          width: 400,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.addModalObj = {
                      modal: true,
                      title: '部门编辑',
                      type: 'modify',
                      data: params.row
                    }
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-school',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.postModalObj = {
                      modal: true,
                      title: params.row.dept_name,
                      data: params.row
                    }
                  }
                }
              }, '职务管理'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-people',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.userModalObj = {
                      modal: true,
                      title: params.row.dept_name,
                      data: params.row
                    }
                  }
                }
              }, '成员管理'),
              // h('Button', {
              //   style: {
              //     margin: '0 5px'
              //   },
              //   props: {
              //     icon: 'md-settings',
              //     size: 'small'
              //   },
              //   on: {
              //     click: () => {
              //       this.configModalObj = {
              //         modal: true,
              //         title: params.row.dept_name,
              //         data: params.row
              //       }
              //     }
              //   }
              // }, '权限管理'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-podium',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.biModalObj = {
                      modal: true,
                      title: params.row.dept_name,
                      data: params.row
                    }
                  }
                }
              }, '权限管理')
            ])
          }
        }
      ]
    }
  },
  methods: {
    // 获取列表数据
    getList () {
      this.loading = true
      API.queryDepartManagePage(this.listQuery).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          this.departList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 添加部门
    addPartModal () {
      this.addModalObj = {
        modal: true,
        title: '部门添加',
        type: 'add',
        data: {}
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  },
  created () {
    this.getList()
  }
})
</script>
<style lang="less">
  .add-part-btn {
    position: absolute;
    right: 16px;
  }
  .table-list {
    margin-top: 40px;
  }
</style>
