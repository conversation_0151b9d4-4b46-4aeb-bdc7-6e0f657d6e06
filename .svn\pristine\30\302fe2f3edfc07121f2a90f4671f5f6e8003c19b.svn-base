<template>
  <div>
    <Tabs type="card" v-model="tabId">
      <TabPane label="用途分析">
        <useforAnalyse v-if="tabId === 0"></useforAnalyse>
      </TabPane>
      <TabPane label="船舶分析">
        <warehousenameAnalyse v-if="tabId === 1"></warehousenameAnalyse>
      </TabPane>
      <TabPane label="时间分析">
        <dateAnalyse v-if="tabId === 2"></dateAnalyse>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import useforAnalyse from './useforAnalyse.vue'
import warehousenameAnalyse from './warehousenameAnalyse.vue'
import dateAnalyse from './dateAnalyse.vue'
export default {
  components: {
    useforAnalyse,
    warehousenameAnalyse,
    dateAnalyse
  },
  data () {
    return {
      tabId: 0
    }
  },
  created () {
  },
  methods: {}
}
</script>
