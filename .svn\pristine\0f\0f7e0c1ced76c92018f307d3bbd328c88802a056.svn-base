<template>
  <div ref="dom" class="charts chart-bar"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'ChartLine',
  props: {
    value: Object,
    unit: String,
    text: String,
    subtext: String,
    clickable: {
      type: Boolean,
      default: false
    },
    legendShow: {
      type: Boolean,
      default: true
    },
    color: {
      type: Array,
      default: () => {
        return ['#ff7f7f', '#ff7fbf', '#ff7fff', '#bf7fff', '#7f7fff', '#7fbfff', '#7fffff', '#7fffbf', '#7fff7f', '#bfff7f', '#DFDFDF']
      }
    },
    rotate: {
      type: String,
      default: '0'
    },
    formatter: {
      type: <PERSON>olean,
      default: false
    },
    showOnemarkLine: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    resize () {
      this.dom.resize()
    },
    chartInit () {
      let _that = this
      this.$nextTick(() => {
        let xAxisData = this.value.xAxis.map(item => item = item.split(',')[0])
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'left',
            textStyle: {
              color: '#2B304C',
              fontSize: '16'
            }
          },
          grid: {
            left: 0,
            right: 120,
            top: 60,
            bottom: 25,
            containLabel: true
          },
          legend: {
            show: this.legendShow,
            icon: 'roundRect',
            data: this.value.legend,
            top: 25
          },
          // toolbox: {
          //   feature: {
          //     // dataView: { show: true, readOnly: false },
          //     magicType: { show: true, type: ['line', 'bar'] },
          //     restore: { show: true }
          //     // saveAsImage: { show: true },
          //   }
          // },
          color: this.color,
          tooltip: {
            trigger: 'axis',
            formatter: item => {
              let str = ''
              if (_that.formatter) {
                str = item[0].name + '<br/>'
                let curRemark = this.value.xAxis.map(e => e = e.substring(11))
                item.forEach(list => {
                  if (item.length === 1) {
                    str += list.marker + list.value + '<br/>' + (curRemark[list.dataIndex] === '' ? '' : '备注：' + curRemark[list.dataIndex])
                  } else if (item.length > 1) {
                    if (!this.showOnemarkLine || this.showOnemarkLine && list.seriesIndex === 0) {
                      str += list.marker + list.seriesName + ':' + list.value + '<br/>' + (list.seriesIndex === 0 || curRemark[list.dataIndex] === '' ? '' : '备注：' + curRemark[list.dataIndex])
                    } else if (this.showOnemarkLine && list.seriesIndex === 1) {
                      str += list.marker + list.seriesName + ':' + this.value.data[1][list.dataIndex].split(',')[1] + '<br/>' + (list.seriesIndex === 0 || curRemark[list.dataIndex] === '' ? '' : '备注：' + curRemark[list.dataIndex])
                    }
                  }
                })
              } else {
                if (item.length > 1) {
                  str = item[0].name + '<br/>'
                  item.forEach(list => {
                    if (!this.showOnemarkLine || this.showOnemarkLine && list.seriesIndex === 0) {
                      str += list.marker + list.seriesName + ':' + list.value + '<br/>'
                    } else if (this.showOnemarkLine && list.seriesIndex === 1) {
                      str += list.marker + list.seriesName + ':' + this.value.data[1][list.dataIndex].split(',')[1] + '<br/>'
                    }
                  })
                } else {
                  str = item[0].name + '<br/>'
                  str += item[0].marker + item[0].value
                }
              }
              return str
            }
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            },
            axisLabel: {
              show: true,
              overflow: 'none',
              ellipsis: 'truncate',
              // interval: '0', // 强制显示文本
              rotate: this.rotate
            },
            data: xAxisData
          },
          yAxis: [],
          series: []
        }
        if (this.value.legend && this.value.legend.length > 1) {
          this.value.data.forEach((item, idx) => {
            option.series.push({
              name: this.value.legend[idx],
              data: item.map((d, index) => {
                if (this.value.xAxis[index].length > 11) {
                  return d = {
                    value: d.split(',')[0],
                    symbol: 'circle',
                    symbolSize: 7
                  }
                } else {
                  return d.split(',')[0]
                }
              }),
              type: 'line',
              showSymbol: this.value.symbol && this.value.symbol.length > 1,
              symbol: this.value.symbol && this.value.symbol.length > 1 ? this.value.symbol[idx] : 'emptyCircle',
              smooth: this.value.smooth !== undefined ? this.value.smooth : true,
              yAxisIndex: this.value.yAxis !== undefined && this.value.yAxis.length > 1 && idx > 0 ? 1 : 0,
              
            })
            if (!this.showOnemarkLine || this.showOnemarkLine && idx !== 0) {
              option.series[idx] = Object.assign(option.series[idx], {
                markLine: {
                  label: {
                    formatter: '{b}: {c}'
                  },
                  data: [{ type: 'average', name: '平均值' }]
                }
              })
            }
          })
        } else {
          option.series.push({
            name: this.value.legend[0],
            data: this.value.data.map((item, idx) => {
              if (this.value.xAxis[idx].length > 11) {
                return item = {
                  value: item,
                  symbol: 'circle',
                  symbolSize: 7
                }
              } else {
                return item
              }
            }),
            type: 'line',
            showSymbol: true,
            symbol: this.value.symbol > 1 ? this.value.symbol : 'emptyCircle',
            smooth: this.value.smooth !== undefined ? this.value.smooth : true,
            lineStyle: {
              width: 3
            },
            markLine: {
              label: {
                formatter: '{b}: {c}'
              },
              data: [{ type: 'average', name: '平均值' }]
            }
          })
        }
        if (this.value.yAxis !== undefined && this.value.yAxis.length > 1) {
          this.value.yAxis.forEach((item, idx) => {
            option.yAxis.push({
              name: item.name,
              type: 'value',
              scale: true,
              splitNumber: 4,
              max: item.max,
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              nameTextStyle: {
                // color: '#4A4A4A',
                fontWeight: '600',
                align: idx > 0 ? 'right' : ''
              }
            })
          })
        } else {
          option.yAxis.push({
            name: this.unit,
            type: 'value',
            scale: true,
            splitNumber: 4,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            nameTextStyle: {
              // color: '#4A4A4A',
              fontWeight: '600',
              align: 'left'
            }
          })
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        this.dom.off('click')
        this.dom.on('click', (params) => {
          if (this.clickable) {
            _that.$emit('clickBack', params)
          }
        })
        on(window, 'resize', this.resize)
      })
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>
