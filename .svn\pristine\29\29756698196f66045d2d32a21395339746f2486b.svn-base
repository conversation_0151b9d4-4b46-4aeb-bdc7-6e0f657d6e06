<template>
  <Card>
    <Tabs @on-click="tabClick">
      <TabPane label="设备管理" name="tab1">
        <Row>
          <Col span="4">
            <Button type="primary" @click="handleEdit">新增设备</Button>
          </Col>
          <Col span="20" style="text-align: right;">
            <Dropdown @on-click="shipChange">
              <a href="javascript:void(0)" v-html="ship_name !== '' ? ship_name : '请选择'" style="color: #515a6e;"></a>
              <Icon type="md-arrow-dropdown"></Icon>
              <Dropdown-menu slot="list">
                <Dropdown-item v-for="(item, idx) in shipList" :name="item.ship_name">{{item.ship_name}}</Dropdown-item>
              </Dropdown-menu>
            </Dropdown>
            <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
          </Col>
        </Row>
        <Row>
          <Col span="4" style="border: 1px solid #ccc;">
            <h3 class="col-text">船舶设备</h3>
            <Tree :data="equipTree" :render="renderContent" class="tree"></Tree>
          </Col>
          <Col span="20" style="padding-left: 10px;">
            <Table border :loading="loading" :columns="columns" :data="equipList" @on-row-click="tableClick"></Table>
            <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
            :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
          </Col>
        </Row>
      </TabPane>
      <TabPane label="设备分类" name="tab2">
        <Button type="primary" @click="handleClassi" style="margin-bottom: 5px;">新增</Button>
        <Table border :loading="loading1" :columns="classiColumns" :data="classiList" maxHeight="500" :draggable="true" @on-drag-drop="onDragDrop"></Table>
      </TabPane>
    </Tabs>
    <!-- 新增编辑弹窗内容 -->
    <Modal v-model="equipModal"
      :title="titleModal"
      @on-ok="handleSubmit('formInline')"
      @on-cancel="handleCancel"
      :width="modalType !== 'file' ? '70%' : '40%'"
      :mask-closable="false">
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="100">
        <div v-if="modalType !== 'file'" class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item label="设备分类">
                <Input type="text" v-model="formInline.name" readonly></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="父设备">
                <div v-if="formInline.equip_pr === ''">--</div>
                <div v-else>{{formInline.equip_pr}}</div>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="系统编码">
                <div>{{formInline.system_code}}</div>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="设备状态">
                <Radio-group v-model="formInline.state" type="button">
                  <Radio label="启用">启用</Radio>
                  <Radio label="停用">停用</Radio>
                </Radio-group>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="equip_name" label="设备名称">
                <Input type="text" v-model="formInline.equip_name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="设备型号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="classi" label="设备级别">
                <Select v-model="formInline.classi" placeholder="请选择">
                  <Option value="一般设备">一般设备</Option>
                  <Option value="安全设备">安全设备</Option>
                  <Option value="关键设备">关键设备</Option>
                  <Option value="应急设备">应急设备</Option>
                </Select>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="制造厂商">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="出厂编号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="生产日期">
                <Date-picker type="date" placeholder="请选择" v-model="formInline.date" style="width: 100%"></Date-picker>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="图纸编号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="证书号">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Form-item label="技术参数">
              <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
            </Form-item>
          </Row>
        </div>
        <div class="tr-name" v-if="modalType !== 'file'">附件</div>
        <fileUpload ref="fileUploadComponent" :fileDataList="fileDataList" type="modalType" @getFileId="getFileId"></fileUpload>
      </Form>
    </Modal>
  </Card>
</template>
<script>
import search from '_c/search'
import fileUpload from '../../../performance/performanceTemp/fileUpload'
import API from '@/api/erpSys/equipManager'
export default {
  components: {
    search,
    fileUpload
  },
  data () {
    return {
      // 设备管理
      ship_name: '',
      shipList: [{
        id: '1',
        ship_name: 'test'
      }], // 储存船名下拉
      equipTreeSeled: {}, // 当前已选中的节点数组
      classiSeleList: [], // 分类下拉选项
      fileDataList: [], // 附件
      wps_ids: '',
      equipModal: false,
      titleModal: '',
      modalType: '',
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '设备名称',
          key: 'name',
          align: 'center'
        },
        {
          title: '设备型号',
          key: '',
          align: 'center'
        },
        {
          title: '制造厂商',
          key: '',
          align: 'center'
        },
        {
          title: '出厂编号',
          key: '',
          align: 'center'
        },
        {
          title: '图纸编号',
          key: '',
          align: 'center'
        },
        {
          title: '生产日期',
          key: 'date',
          align: 'center'
        },
        {
          title: '设备级别',
          key: 'level',
          align: 'center',
          filters: [
            {
              label: '一般设备',
              value: '一般'
            },
            {
              label: '安全设备',
              value: '安全'
            },
            {
              label: '关键设备',
              value: '关键'
            },
            {
              label: '应急设备',
              value: '应急'
            }
          ],
          filterMethod (value, row) {
            return row.level.indexOf(value) > -1
          }
        },
        {
          title: '设备状态',
          key: 'status',
          align: 'center',
          filters: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 2
            }
          ],
          filterMultiple: false,
          filterMethod (value, row) {
            if (value === 1) {
              return row.status === 1
            } else if (value === 2) {
              return row.status === 2
            }
          }
        },
        {
          title: '附件',
          key: '',
          align: 'center',
          width: 120,
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-link',
                size: 'small',
                type: 'text'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.modalType = 'file'
                  this.titleModal = '附件信息'
                  this.equipModal = true
                  this.handleUpload(params.row.id)
                }
              }
            }, // params.row.fileArr.length
            )
          }
        },
        {
          title: '操作',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-trash',
                size: 'small'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.handleDelete(params.row)                  
                }
              }
            })
          }
        }
      ],
      equipTree: [
        {
          title: '主推及辅助设备类',
          // expand: true,
          render: (h, { root, node, data }) => { // root根节点, node当前节点, data
            return h('span', {
              style: {
                display: 'inline-block',
                width: '100%'
              },
              on: {
                click: () => {
                  // this.equipTreeSeled = data
                  console.log(data)
                  this.getequipList(data)
                  this.equipTreeSeled.name = data.title
                }
              }
            }, [
              h('span', [
                h('Icon', {
                  props: {
                    type: 'ios-home-outline'
                  },
                  style: {
                    marginRight: '8px'
                  }
                }),
                h('span', data.title)
              ])
            ])
          },
          children: [
            {
              title: '主机',
              children: [{
                title: 'test111'
              }]
            },
            {
              title: '供油单元'
            },
            {
              title: '淡水冷却器'
            },
            {
              title: '滑油冷却器'
            }
          ]
        }
      ],
      equipList: [{
        name: '主机',
        date: '2012-12-06'
      }], // 设备列表
      loading: false,
      total: 0,
      listQuery: {
        number: '03',
        createorg_number: 'xt',
        level: '2',
        pageSize: 10,
        pageNo: 1,
        access_token: localStorage.getItem('access_token')
      },
      listCurrent: 1,
      setSearchData: {
        member_name: {
          type: 'text',
          label: '共条结果',
          width: 180,
          value: '',
          isdisable: false,
          placeholder: '请输入设备名称、系统编码、设备型号、制造厂商'
        }
      },
      formInline: {
        equip_pr: '',
        system_code: 'A01',
        state: '启用'
      },
      ruleInline: {
        equip_name: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      shipSelList: [{
        id: '5',
        name: '兴通789'
      }], // 适用船舶下拉
      // 设备分类
      currentId: '', // 当前编辑的id
      currentVal: '', // 存储当前编辑的值
      loading1: false,
      classiList: [{
        classi_name: '主推及辅助设备类',
        id: '1',
        code: 'CF',
        ship_name: 'xingtong16'
      }, {
        classi_name: '发电及货泵柴油机',
        id: '2',
        code: 'J',
        ship_name: 'xingtong9'
      }],
      classiColumns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '分类编码',
          key: 'code',
          width: 120,
          align: 'center'
        },
        {
          title: '分类名称',
          key: 'classi_name',
          align: 'center',
          width: 300,
          render: (h, params) => {
            const inp = h('Input', {
              on: {
                input: (val) => {
                  this.currentVal = val
                }
              }
            }, params.row.classi_name)
            return this.currentId === params.row.id ? inp : h('span', params.row.classi_name)
          }
        },
        {
          title: '适用船舶',
          key: '',
          align: 'center',
          render: (h, params) => {
            const inp = h('Select', {
              props: {
                'label-in-value': true,
                value: params.row.id
              },
              on: {
                'on-change': (val) => {
                  this.currentVal = val.label
                }
              }
            },
            this.shipSelList.map(item => {
              return h('Option', {
                props: {
                  value: item.id,
                  label: item.name
                }
              })
            })
          )
          return this.currentId === params.row.id ? inp : h('span', params.row.ship_name)
          }
        },
        {
          title: '拖拽排序',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'ios-paper',
                size: 'small'
              },
              on: {
                change: () => {
                  this.getClassiList()
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: '',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('i-button', {
                props: {
                  icon: this.currentId === params.row.id ? 'ios-folder' : 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    if (this.currentId === params.row.id) {
                      this.handleSave(params.row)
                    } else {
                      this.currentId = params.row.id
                    }
                  }
                }
              }),
              h('Button', {
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, 'classi')
                  }
                }
              })
            ])
          }
        }
      ]
    }
  },
  created () {
    this.getequipList()
  },
  methods: {
    // tab切换
    tabClick (name) {
      if (name === 'tab1') {
        this.getequipList()
      } else {
        this.getClassiList()
      }
    },
    // 树节点
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
          click: () => {
            console.log(data)
            this.getequipList(data)
          }
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', data.title)
        ])
      ])
    },
    // 获取设备列表
    getequipList (d) {
      // this.listQuery.classi_id = d
      this.loading = true
      console.log(this.listQuery)
      API.getEquipList(this.listQuery).then(res => {
        console.log(res)
        if (res.data.Code === 10000) {
          this.loading = false
          this.equipList = res.data.Result
          this.total = res.data.Total
        } else {
          // this.$Message.error(res.data.Message)
        }
      })
    },
    // 新增设备
    handleEdit () {
      if (this.equipTreeSeled.name === undefined) {
        this.$Message.error('请先选择设备分类或设备')
      } else {
        this.titleModal = '新增设备信息'
        this.equipModal = true
        this.formInline.name = this.equipTreeSeled.name
        this.formInline.code = this.equipTreeSeled.code
      }
    },
    // 编辑设备信息
    tableClick (list) {
      this.equipModal = true
      this.titleModal = '编辑设备信息'
      this.formInline = list
      this.formInline.name = this.equipTreeSeled.name
      this.formInline.code = this.equipTreeSeled.code
      // this.fileDataList = list.files
    },
    // 保存
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认保存设备信息？</p>',
            loading: true,
            onOk: () => {
              API(this.formInline).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.$refs['name'].resetFields()
                  this.getequipList(this.equipTree[0].id)
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 关闭弹窗
    handleCancel () {
      this.modalType = ''
      this.equipModal = false
      this.$refs['formInline'].resetFields()
      this.formInline.date = ''
      this.formInline.state = '启用'
    },
    // 删除列表
    handleDelete (row, type) {
      if (type && row.id === '') {
        this.classiList.splice(row, 1)
      } else {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>删除后无法恢复，是否确认删除？</p>',
          loading: true,
          onOk: () => {
            if (type) { // 分类列表
              // API.({ id: row.id }).then(res => {
              //   if (res.data.Code === 10000) {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.success(res.data.Message)
              //     this.getequipList(this.equipTree[0].id)
              //   } else {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.error(res.data.Message)
              //   }
              // })
            } else { // 设备管理列表
              // API.({ id: row.id }).then(res => {
              //   if (res.data.Code === 10000) {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.success(res.data.Message)
              //     this.getequipList(this.equipTree[0].id)
              //   } else {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.error(res.data.Message)
              //   }
              // })
            }
          }
        })
      }
    },
    // 附件上传 
    handleUpload (d) {},
    getFileId () {
      this.wps_ids = this.fileDataList.map(item => {
        return item.id
      }).join()
    },
    // 船名搜索
    shipChange (name) {
      this.ship_name = name
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getequipList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.question_topic.value = ''
      this.getequipList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getequipList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getequipList()
    },
    // ...设备分类...//
    // 获取分类列表
    getClassiList () {
      // this.loading1 = true
      // API().then(res => {
      //   if (res.data.Code === 10000) {
      //     this.loading1 = false
      //     this.classiList = res.data.Result
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    // 新增分类
    handleClassi () {
      if (this.classiList[0].id === '') {
        this.$Message.error('请先保存后，才能操作下一条！')
      } else {
        this.classiList.unshift({
          id: '',
          classi_name: ''
        })
      }
    },
    // 保存分类列表编辑
    handleSave (row) {
      if (row.code === '' || row.classi_name === '' || row.type === '') {
        this.$Message.error('带*号的为必填项')
      } else {
        this.classiList = this.classiList.map(v => {
          return v.id === row.id ? { ...v, ...this.currentVal } : v
        })
        this.currentId = ''
        this.currentVal = ''
      }
    },
    // 拖拽排序
    onDragDrop(first, end) {
      //转成int型，方便后续使用
      first = parseInt(first)
      end = parseInt(end)
      let tmp = this.classiList[first]
      if(first < end) {
        for(var i=first+1; i<=end; i++) {
          this.classiList.splice(i-1, 1, this.classiList[i])
        }
        this.classiList.splice(end, 1, tmp)
      }
      if(first > end) {
        for(var i=first; i>end; i--) {
          this.classiList.splice(i, 1, this.classiList[i-1])
        }
        this.classiList.splice(end, 1, tmp)
      }
      // let idx = this.classiList.findIndex(e => {return e.id === ''})
      // if (idx > -1) {
      //   this.classiList.splice(this.classiList[idx], 1)
      // }
      if (this.classiList[0].id === '') {
        this.classiList.splice(this.classiList[0], 1)
      }
      this.getClassiList()
    }
  }
}
</script>
<style lang="less" scoped>
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.search-vie {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
</style>
<style>
.tree.ivu-tree ul li {
  margin: 5px 0;
}
.tree .ivu-tree-title span {
  font-size: 16px;
  color: #666;
}
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
}
</style>
