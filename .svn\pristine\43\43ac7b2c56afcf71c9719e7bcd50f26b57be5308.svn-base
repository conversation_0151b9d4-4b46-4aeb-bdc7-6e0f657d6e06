<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="500" @on-visible-change="modalShowHide" :mask-closable="false" ok-text="保存" @on-ok=
  "saveHandle">
    <div v-if="configList.length === 0" class="null-data">暂无数据，请先添加职务列表</div>
    <div v-for="(item, idx) in configList" :key="item.post_id">
      <div class="post-title">{{ item.post_name }}</div>
      <CheckboxGroup v-model="moduleList[idx]">
        <Checkbox v-for="list in item.configs" :key="list.module_id" :label="list.module_id">
          <span>{{ list.module_name }}</span>
        </Checkbox>
      </CheckboxGroup>
    </div>
  </Modal>
</template>
<script>
import API from '@/api/jurisdictionManage/moduleManagement'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      configList: [], // 权限列表
      moduleList: [], // 保存的列表数据
      configData: {} // 父级带回来数据
    }
  },
  methods: {
    // 获取部门权限列表
    getList () {
      API.queryAllModuleList({
        dept_id: this.configData.dept_id
      }).then(res => {
        if (res.data.Code === 10000) {
          this.configList = res.data.Result
          if (res.data.Result && res.data.Result.length > 0) {
            res.data.Result.forEach((item, idx) => {
              this.moduleList[idx] = []
              item.configs.forEach(list => {
                if (list.config_flag === '1') {
                  this.moduleList[idx].push(list.module_id)
                }
              })
            })
          }
        }
      })
    },
    // 保存数据
    saveHandle () {
      let _that = this
      let paramList = []
      this.moduleList.forEach((item, idx) => {
        item.map(list => {
          paramList.push({
            post_id: _that.configList[idx].post_id,
            module_id: list
          })
        })
      })
      API.configPostModule({
        dept_id: this.configData.dept_id,
        configs: JSON.stringify(paramList)
      }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 弹穿显隐显示
    modalShowHide (val) {
      if (val) {
        this.configData = this.modalData.data
        this.getList()
      }
    }
  }
})
</script>
<style lang="less">
  .post-title {
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    font-weight: bold;
    margin-top: 5px;
  }
</style>
