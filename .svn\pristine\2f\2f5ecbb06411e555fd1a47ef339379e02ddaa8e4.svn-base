/* jshint esversion: 6 */
import Main from '@/components/main'
import '@/assets/menu_icon/iconfont.css'

/**
 * 这里可以把一些不需要管的路由留下，比如首页，登录，401 ，405。404别留这里，要最后一个动态加载到最末尾
 */
export default [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      hideInMenu: true
    },
    component: () => import ('@/view/login/login.vue')
  },
  {
    path: '/viewFile',
    name: 'viewFile',
    meta: {
      title: '文件预览编辑',
      hideInMenu: true
    },
    component: () => import ('@/view/wpsView/viewFile.vue')
  },
  {
    path: '/',
    name: '_home',
    redirect: '/home',
    component: Main,
    meta: {
      hideInMenu: false,
      notCache: true
    },
    children: [{
      path: '/home',
      name: 'home',
      meta: {
        hideInMenu: false,
        title: '首页',
        notCache: true,
        icon: 'md-apps'
      },
      component: () => import ('@/view/single-page/home')
    }]
  },
  // 采购数据分析
  {
    path: '/',
    name: 'materialPrice',
    component: Main,
    meta: {
      isCustom: true,
      icon: 'icon icon-menu_icon1',
      title: '物料备件价格',
      access: ['materialPurchase']
    },
    children: [
      {
        path: 'materialSinglePrice',
        name: 'materialSinglePrice',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon8',
          title: '单种价格走势'
        },
        component: () => import('@/view/materialPurchaseSystem/materialSinglePrice/index.vue')
      },
      {
        path: 'materialTotalPrice',
        name: 'materialTotalPrice',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon3',
          title: '总金额分析'
        },
        component: () => import('@/view/materialPurchaseSystem/materialTotalPrice/index.vue')
      },
      {
        path: 'compareAnalysis',
        name: 'compareAnalysis',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon9',
          title: '对比分析'
        },
        component: () => import('@/view/materialPurchaseSystem/compareAnalysis/index')
      },
      {
        path: 'oilPrice',
        name: 'oilPrice',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon4',
          title: '油料分析'
        },
        component: () => import('@/view/materialPurchaseSystem/oilPrice/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'materialNum',
    component: Main,
    meta: {
      isCustom: true,
      icon: 'icon icon-menu_icon5',
      title: '物料备件数量',
      access: ['materialPurchase']
    },
    children: [
      {
        path: 'materialSingleNum',
        name: 'materialSingleNum',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon6',
          title: '单种采购量'
        },
        component: () => import('@/view/materialPurchaseSystem/materialSingleNum/index.vue')
      },
      {
        path: 'materialShipNum',
        name: 'materialShipNum',
        meta: {
          icon: 'md-boat',
          title: '船舶采购量'
        },
        component: () => import('@/view/materialPurchaseSystem/materialShipNum/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'configure',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '设置',
      access: ['materialPurchase']
    },
    children: [
      {
        path: 'dataConfigure',
        name: 'dataConfigure',
        meta: {
          icon: 'md-lock',
          title: '数据配置'
        },
        component: () => import('@/view/materialPurchaseSystem/dataConfigure/index')
      },
      {
        path: 'trilateralOilPrice',
        name: 'trilateralOilPrice',
        meta: {
          icon: 'ios-pin',
          title: '三方价格'
        },
        component: () => import('@/view/materialPurchaseSystem/trilateralOilPrice/index')
      },
      {
        path: 'areaConfigure',
        name: 'areaConfigure',
        meta: {
          icon: 'ios-pin',
          title: '区域配置'
        },
        component: () => import('@/view/materialPurchaseSystem/areaConfigure/index')
      }
    ]
  },
  // 船员大数据
  {
    path: '/',
    name: 'shipperOwnerManage',
    component: Main,
    meta: {
      icon: 'md-contacts',
      title: '船员大数据',
      access: ['shipperOwner']
    },
    children: [
      {
        path: 'shipperOwner',
        name: 'shipperOwner',
        meta: {
          icon: 'md-contacts',
          title: '船员大数据'
        },
        component: () => import('@/view/shipperOwner/index/index.vue')
      },
      {
        path: 'shipperOwnerDetail/:id',
        name: 'shipperOwnerDetail',
        meta: {
          icon: 'md-contacts',
          title: '船员大数据详情',
          hideInMenu: true
        },
        component: () => import('@/view/shipperOwner/index/shipperOwnerDetail.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'shipperOwnerSet',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '配置',
      access: ['shipperOwner']
    },
    children: [
      {
        path: 'shipperOwnerSet',
        name: 'shipperOwnerSet',
        meta: {
          icon: 'md-cog',
          title: '配置'
        },
        component: () => import('@/view/shipperOwner/set/index.vue')
      }
    ]
  },
  // 后台管理
  {
    path: '/',
    name: 'jurisdictionManage',
    component: Main,
    meta: {
      icon: 'ios-albums-outline',
      title: '组织架构',
      access: ['organizational']
    },
    children: [
      {
        path: 'userManagement',
        name: 'userManagement',
        meta: {
          icon: 'md-person',
          title: '用户管理'
        },
        component: () => import('@/view/jurisdictionManage/userManagement')
      },
      {
        path: 'departmentManagement',
        name: 'departmentManagement',
        meta: {
          icon: 'md-people',
          title: '部门管理'
        },
        component: () => import('@/view/jurisdictionManage/departmentManagement')
      },
      {
        path: 'moduleManagement',
        name: 'moduleManagement',
        meta: {
          icon: 'md-cog',
          title: '模块管理'
        },
        component: () => import('@/view/jurisdictionManage/moduleManagement')
      }
    ]
  },
  // 考试系统
  {
    path: '/',
    name: 'examModule',
    component: Main,
    meta: {
      icon: 'ios-albums-outline',
      title: '考试模块',
      access: ['examSystem']
    },
    children: [
      {
        path: 'memberManagement',
        name: 'memberManagement',
        meta: {
          icon: 'md-person-add',
          title: '成员管理'
        },
        component: () => import('@/view/examSystem/examModule/memberManagement')
      },
      {
        path: 'questionBankList',
        name: 'questionBankList',
        meta: {
          icon: 'md-list',
          title: '题库列表'
        },
        component: () => import('@/view/examSystem/examModule/questionBankList')
      },
      {
        path: 'questionBankClassify',
        name: 'questionBankClassify',
        meta: {
          icon: 'ios-folder-outline',
          title: '题库类别'
        },
        component: () => import('@/view/examSystem/examModule/questionBankClassify/index.vue')
      },
      {
        path: 'jobCategory',
        name: 'jobCategory',
        meta: {
          icon: 'md-person',
          title: '归属分类'
        },
        component: () => import('@/view/examSystem/examModule/jobCategory/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'trainingModule',
    component: Main,
    meta: {
      icon: 'md-book',
      title: '培训模块',
      access: ['examSystem']
    },
    children: [
      {
        path: 'trainingTopics',
        name: 'trainingTopics',
        meta: {
          icon: 'ios-paper',
          title: '培训管理'
        },
        component: () => import('@/view/examSystem/trainingModule/trainingTopics')
      },
      {
        path: 'memberConfigure',
        name: 'memberConfigure',
        meta: {
          icon: 'md-people',
          title: '成员信息'
        },
        component: () => import('@/view/examSystem/trainingModule/memberManagement')
      },
      {
        path: 'memberStatistics',
        name: 'memberStatistics',
        meta: {
          icon: 'md-podium',
          title: '统计分析'
        },
        component: () => import('@/view/examSystem/trainingModule/memberStatistics')
      }
    ]
  },
  {
    path: '/',
    name: 'setConfigure',
    component: Main,
    meta: {
      icon: 'ios-settings',
      title: '设置',
      access: ['examSystem']
    },
    children: [
      {
        path: 'evaluateTemplate',
        name: 'evaluateTemplate',
        meta: {
          icon: 'ios-create-outline',
          title: '评价模板'
        },
        component: () => import('@/view/examSystem/dataConfigure/evaluateTemplate')
      }
    ]
  },
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import ('@/view/error-page/401.vue')
  },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import ('@/view/error-page/500.vue')
  }
]
