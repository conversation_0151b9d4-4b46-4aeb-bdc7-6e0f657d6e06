import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 用户管理--获取用户下拉框
export function queryAllStaffList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/staff/queryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--部门成员列表 分页
export function queryPostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/queryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--添加部门成员
export function addPostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/add',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--修改部门成员
export function updatePostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--删除部门成员
export function delPostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/delete',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
