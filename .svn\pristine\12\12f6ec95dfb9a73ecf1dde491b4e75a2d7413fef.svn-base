<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="1000" @on-visible-change="modalShowHide" :mask-closable="false">
    <Button class="add-part-btn" type="primary" @click="addUserModal">添加成员</Button>
    <Table class="table-list" border :loading="loading" ref="selection" :columns="columns" :data="userList"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <div slot="footer">
      <Button type="primary" @click="backModal">返回</Button>
    </div>
    <UserEditModal :modalData="userDataObj" @userDataBack="getList"></UserEditModal>
  </Modal>
</template>
<script>
import { queryPostStaffPage, delPostStaffPage } from '@/api/jurisdictionManage/userManagement'
import UserEditModal from './userEditModal.vue'

export default ({
  components: {
    UserEditModal
  },
  props: {
    modalData: Object
  },
  data () {
    return {
      loading: false,
      total: 0,
      listCurrent: 1,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      userList: [], // 成员列表
      userDataObj: {
        modal: false,
        type: '',
        title: '',
        data: {}
      },
      columns: [
        {
          type: 'index',
          width: 50,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'user_name',
          align: 'center',
          width: 100
        },
        {
          width: 120,
          title: '一体化账号',
          key: 'unified_account',
          align: 'center'
        },
        {
          title: '瀛海账号',
          key: 'erp_account',
          align: 'center'
        },
        {
          title: '海运管家账号',
          key: 'hzx_account',
          width: 115,
          align: 'center'
        },
        {
          title: '职务',
          key: 'post_name',
          align: 'center'
        },
        {
          title: '绩效资金额度',
          key: 'perf_bonus',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.userDataObj = {
                      modal: true,
                      type: 'modify',
                      title: '成员编辑',
                      data: params.row
                    }
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.$Modal.confirm({
                      title: '提示',
                      content: '<p>确认删除该成员？</p>',
                      loading: true,
                      onOk: () => {
                        delPostStaffPage({
                          dept_staff_post_id: params.row.dept_staff_post_id
                        }).then(res => {
                          this.loading = false
                          this.$Modal.remove()
                          if (res.data.Code === 10000) {
                            this.$Message.success(res.data.Message)
                            this.getList()
                          } else {
                            this.$Message.error(res.data.Message)
                          }
                        })
                      }
                    })
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  methods: {
    getList () {
      this.loading = true
      queryPostStaffPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.userList = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 添加职务
    addUserModal () {
      this.userDataObj = {
        modal: true,
        type: 'add',
        title: '新增成员',
        data: {
          dept_id: this.modalData.data.dept_id,
          dept_name: this.modalData.data.dept_name
        }
      }
    },
    backModal () {
      this.modalData.modal = false
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    modalShowHide (val) {
      Object.assign(this.listQuery, {
        dept_id: this.modalData.data.dept_id
      })
      this.getList()
    }
  }
})
</script>
<style lang="less">
  .add-part-btn {
    position: absolute;
    right: 16px;
  }
  .table-list {
    margin-top: 40px;
  }
</style>
