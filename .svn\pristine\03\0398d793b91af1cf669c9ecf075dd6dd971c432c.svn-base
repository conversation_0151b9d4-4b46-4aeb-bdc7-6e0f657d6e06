<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import { getMultiInfosPage } from '@/api/jurisdictionManage/userManagement'
export default {
  components: {
    search
  },
  data () {
    return {
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        staff_name: '',
        staff_mobile_phone: '',
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'staff_name',
          align: 'center'
        },
        {
          title: '账号',
          key: 'staff_mobile_phone',
          align: 'center'
        },
        {
          title: '用户名',
          key: 'login_name',
          align: 'center'
        },
        {
          title: '部门',
          key: 'dept_name',
          align: 'center'
        },
        {
          title: '职务',
          key: 'post_name',
          align: 'center'
        }
      ],
      setSearchData: {
        staff_name: {
          type: 'text',
          label: '姓名',
          width: 180,
          value: '',
          isdisable: false
        },
        staff_mobile_phone: {
          type: 'text',
          label: '账号',
          width: 180,
          value: '',
          isdisable: false
        }
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      getMultiInfosPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listQuery.staff_name = e.staff_name
      this.listQuery.staff_mobile_phone = e.staff_mobile_phone
      delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.setSearchData.staff_name.value = ''
      this.setSearchData.staff_mobile_phone.value = ''
      this.listCurrent = 1
      this.listQuery = {        
        staff_name: '',
        staff_mobile_phone: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
