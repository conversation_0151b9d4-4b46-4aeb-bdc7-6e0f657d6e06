import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 物料备件数量单种数量-月份分页报表
export function queryMaterialMonthPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialAndDetailSingleList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 物料备件数量单种数量-日期分页报表
export function queryMaterialDatePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialSingleByDay',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 物料备件数量单种数量-日期分页报表
export function queryMaterialShipPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/getUnifiedMaterialQuantitySumGroupByWarehousename',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
