<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import { queryQuestionTypeList } from '@/api/jurisdictionManage/userManagement'
export default {
  components: {
    search
  },
  data () {
    return {
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'name',
          align: 'center'
        },
      ],
      setSearchData: {
        name: {
          type: 'text',
          label: '姓名',
          width: 180,
          value: '',
          isdisable: false
        },
        num: {
          type: 'text',
          label: '账号',
          width: 180,
          value: '',
          isdisable: false
        }
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      // this.loading = true
      // queryQuestionTypeList(this.listQuery).then(res => {
      //   if (res.data.Code === 10000) {
      //     this.loading = false
      //     this.list = res.data.Result
      //     this.total = res.data.Total
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
