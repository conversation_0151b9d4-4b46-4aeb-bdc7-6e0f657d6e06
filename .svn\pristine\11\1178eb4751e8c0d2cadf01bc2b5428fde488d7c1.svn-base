import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取用途下拉数据
export function materialUsefor (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialDetailUsefor',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取名称下拉数据
export function materialInventoryname (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialDetailInventoryname',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取型号下拉数据
export function materialInventorystd (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialDetailInventorystd',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船舶下拉数据
export function materialWarehousename (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialWarehousename',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取供应商下拉数据
export function materialVendorname (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialVendorname',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 系统时间
export function getSysTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/getSysTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 数据同步
export function dataToSync (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/material/data/resource/addMaterialSourceDataToDataBase',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加/修改备注，油料价格-日期分析区域修改
export function addOrUpdateAffiliate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/nuified/material/affiliate/addOrUpdateUnifiedMaterialAffiliate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询缓存中的字典条目-无分页
export function queryDictEntryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/queryDictCacheList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  materialUsefor,
  materialInventoryname,
  materialInventorystd,
  materialWarehousename,
  materialVendorname,
  getSysTime,
  dataToSync,
  addOrUpdateAffiliate,
  queryDictEntryList
}