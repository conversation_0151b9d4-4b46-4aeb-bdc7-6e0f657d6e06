<template>
  <div>
    <template v-for="(item, index) in setFormAction.operation">
      <Button v-if="item==='detail'" :key="index" type="primary" icon="md-paper" @click="handleDetail">查看</Button>
      <Button v-if="!routeName && item==='create' && canModify" :key="index" type="primary" icon="md-add-circle" @click="handleCreate">新增</Button>
      <Button v-if="routeName && item==='create' && canModify" :key="index" type="primary" icon="md-add-circle" @click="handleCreate">添加公司</Button>
      <Button v-if="item==='modify'" :key="index" type="primary" icon="md-brush" @click="handleModify">编辑</Button>
      <Button v-if="item==='delete'" :key="index" type="primary" icon="md-trash" @click="handleDelete">删除</Button>
      <Button v-if="item==='updateTable'" :key="index" type="primary" icon="md-refresh" @click="handleUpdateTable">更新</Button>
    </template>
  </div>
</template>

<script>
export default {
  props: ['setFormAction'],
  data () {
    return {
      routeName: true,
      canModify: this.$store.state.user.canModify
    }
  },
  created () {
    if (this.$route.name === 'customerManagement') {
      this.routeName = true
    } else {
      this.routeName = false
    }
  },
  methods: {
    handleDetail () { // 查看
      this.$emit('handleDetail')
    },
    handleCreate () { // 新增
      this.$emit('handleCreate')
    },
    handleModify () { // 编辑
      this.$emit('handleModify')
    },
    handleDelete () { // 删除
      this.$emit('handleDelete')
    },
    handleUpdateTable () { // 更新
      this.$emit('handleUpdateTable')
    }
  }
}
</script>

<style scoped>
button{
  margin-left: 5px;
}
.upload{
  float: right
}
</style>
