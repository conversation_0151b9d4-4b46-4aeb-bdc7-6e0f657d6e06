import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 课题数量&培训时长 总计
export function queryTrainThemeStatInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/queryTrainThemeStatInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 培训总时长/签到次数 分页
export function queryTrainMemberDurationPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/member/queryTrainMemberDurationStatPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 培训总时长/签到次数 导出
export function exportTrainDuration (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: ' /report/train/theme/trainMemberDurationStatTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 考试平均成绩/次数 分页
export function queryTrainMemberExamAvgPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/member/queryTrainMemberExamAvgStatPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 讲师评分/参与人数 分页
export function queryTrainThemeLecturerPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/queryTrainThemeLecturerStatPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryTrainThemeStatInfo,
  queryTrainMemberDurationPage,
  exportTrainDuration,
  queryTrainMemberExamAvgPage,
  queryTrainThemeLecturerPage
}