<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <Button class="user_add_btn" type="primary" width="120" @click="addManage">新增</Button>
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <EditModal :modalData="modalData" @editBack="getList"></EditModal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import { queryUnifiedAccountPage, delUnifiedAccount, updateUnifiedAccount, resetUnifiedAccountPw } from '@/api/jurisdictionManage/userManagement'
import EditModal from './editModal.vue'
export default {
  components: {
    search,
    EditModal
  },
  data () {
    return {
      modalData: {
        title: '',
        modal: false,
        type: '',
        data: {}
      },
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        user_name: '',
        unified_account: '',
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'user_name',
          align: 'center',
          width: 90
        },
        {
          title: '一体化平台账号',
          key: 'unified_account',
          align: 'cener',
          width: 140
        },
        {
          title: '瀛海账号',
          key: 'erp_account',
          align: 'center',
          width: 100
        },
        {
          title: '海运管家账号',
          key: 'hzx_account',
          align: 'center',
          width: 140
        },
        {
          title: '部门',
          key: 'dept_name',
          align: 'center'
        },
        {
          title: '职务',
          key: 'post_name',
          align: 'center'
        },
        {
          title: '状态',
          key: 'status_value',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.status_value === '1' ? '在职' : '离职')
          }
        },
        {
          title: '操作',
          key: 'login_name',
          align: 'center',
          width: 280,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleEdit(params.row)
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-bug',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleResetPw(params.row)
                  }
                }
              }, '重置密码'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      setSearchData: {
        user_name: {
          type: 'text',
          label: '姓名',
          width: 180,
          value: '',
          isdisable: false
        },
        unified_account: {
          type: 'text',
          label: '账号',
          width: 180,
          value: '',
          isdisable: false
        }
      }
    }
  },
  created () {
    let _isManage = (localStorage.getItem('userDataId') === '320068E504CD43AF9A94123391F14021')
    if (_isManage) {
      this.columns.splice(-1, 0, {
        title: '管理员权限',
        key: 'admin_rights',
        align: 'center',
        render: (h, params) => {
          return h('i-switch', {
            props: {
              value: params.row.admin_rights === '1'
            },
            on: {
              'on-change': () => {
                params.row.admin_rights = params.row.admin_rights === '0' ? '1' : '0'
                this.changeAdminRights(params.row)
              }
            }
          })
        }
      })
    }
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      queryUnifiedAccountPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 新增用户
    addManage () {
      this.modalData = {
        modal: true,
        title: '新增用户',
        type: 'add',
        data: {}
      }
    },
    // 用户编辑
    handleEdit (row) {
      this.modalData = {
        modal: true,
        title: '新增用户',
        type: 'modify',
        data: row
      }
    },
    // 用户删除
    handleDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定删除该用户信息？</p>',
        loading: true,
        onOk: () => {
          delUnifiedAccount({ unified_account_id: row.unified_account_id }).then(res => {
            this.$Modal.remove()
            this.loading = false
            if (res.data.Code === 10000) {
              this.getList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    handleResetPw (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定重置该用户密码？</p>',
        loading: true,
        onOk: () => {
          resetUnifiedAccountPw({ unified_account_id: row.unified_account_id }).then(res => {
            this.$Modal.remove()
            this.loading = false
            if (res.data.Code === 10000) {
              this.getList()
              this.$Message.success(res.data.Message)
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    // 管理员权限调整
    changeAdminRights (item) {
      let _param = {
        unified_account_id: item.unified_account_id,
        user_name: item.user_name,
        unified_account: item.unified_account,
        admin_rights: item.admin_rights
      }
      updateUnifiedAccount(_param).then(res => {
        if (res.data.Code === 10000) {
          // this.getList()
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listQuery.user_name = e.user_name
      this.listQuery.unified_account = e.unified_account
      delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.setSearchData.user_name.value = ''
      this.setSearchData.unified_account.value = ''
      this.listCurrent = 1
      this.listQuery = {
        user_name: '',
        unified_account: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped>
  .user_add_btn {
    position: absolute;
    right: 20px;
    top: 20px;
  }
</style>
