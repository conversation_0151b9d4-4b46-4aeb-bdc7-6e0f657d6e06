import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 船员信息列表-分页
export function queryShipperList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/info/querySeafarerInfoPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船员信息列表-不分页
export function querySeafarerInfoList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/info/querySeafarerInfoList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询船员基础数据
export function querySeafarerExtrasList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/extras/querySeafarerExtrasList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 保存船员基础数据
export function updateSeafarerExtras (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/extras/addOrUpdateSeafarerExtras',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船员关联航次信息 汇总
export function getSeafarerInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/info/getSeafarerInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询证书主次信息
export function queryCertificateAndDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/certificate/querySeafarerCertificateAndDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询外部检查主次信息
export function queryInspectionAndDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/inspection/querySeafarerInspectionAndDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询荣誉主次信息
export function queryHonorAndDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/honor/querySeafarerHonorAndDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船员关联航次信息 汇总
export function querySeafarerServiceAndDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/service/querySeafarerServiceAndDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量修改 船员服务年限
export function updateSeafarerService (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/service/bathUpdateSeafarerService',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量修改 船员持有证书
export function updateSeafarerCertificate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/certificate/batchUpdateSeafarerCertificate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量修改 船员外部检查
export function updateSeafarerInspection (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/inspection/batchUpdateSeafarerInspection',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量修改 船员船员荣誉
export function updateSeafarerHonor (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/honor/batchUpdateSeafarerHonor',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
