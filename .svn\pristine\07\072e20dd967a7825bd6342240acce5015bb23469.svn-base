/* jshint esversion: 6 */
import Main from '@/components/main'
import '@/assets/menu_icon/iconfont.css'

/**
 * 这里可以把一些不需要管的路由留下，比如首页，登录，401 ，405。404别留这里，要最后一个动态加载到最末尾
 */
export default [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      hideInMenu: true
    },
    component: () => import ('@/view/login/login.vue')
  },
  {
    path: '/viewFile',
    name: 'viewFile',
    meta: {
      title: '文件预览编辑',
      hideInMenu: true
    },
    component: () => import ('@/view/wpsView/viewFile.vue')
  },
  {
    path: '/',
    name: '_home',
    redirect: '/home',
    component: Main,
    meta: {
      hideInMenu: false,
      notCache: true
    },
    children: [{
      path: '/home',
      name: 'home',
      meta: {
        hideInMenu: false,
        title: '首页',
        notCache: true,
        icon: 'md-apps'
      },
      component: () => import ('@/view/single-page/home')
    }]
  },
  {
    path: '/',
    name: 'materialPrice',
    component: Main,
    meta: {
      isCustom: true,
      icon: 'icon icon-menu_icon1',
      title: '物料备件价格',
      access: ['materialPurchase']
    },
    children: [
      {
        path: 'materialSinglePrice',
        name: 'materialSinglePrice',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon8',
          title: '单种价格走势'
        },
        component: () => import('@/view/materialSinglePrice/index.vue')
      },
      {
        path: 'materialTotalPrice',
        name: 'materialTotalPrice',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon3',
          title: '总金额分析'
        },
        component: () => import('@/view/materialTotalPrice/index.vue')
      },
      {
        path: 'compareAnalysis',
        name: 'compareAnalysis',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon9',
          title: '对比分析'
        },
        component: () => import('@/view/compareAnalysis/index')
      },
      {
        path: 'oilPrice',
        name: 'oilPrice',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon4',
          title: '油料分析'
        },
        component: () => import('@/view/oilPrice/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'shipperOwner',
    component: Main,
    meta: {
      isCustom: true,
      icon: 'md-contacts',
      title: '船员大数据',
      access: ['shipperOwner']
    },
    children: [
      {
        path: 'shipperOwner',
        name: 'shipperOwner',
        meta: {
          isCustom: true,
          icon: 'md-contacts',
          title: '船员大数据'
        },
        component: () => import('@/view/shipperOwner/index/index.vue')
      },
      {
        path: 'shipperOwnerDetail/:id',
        name: 'shipperOwnerDetail',
        meta: {
          isCustom: true,
          icon: 'md-contacts',
          title: '船员大数据详情',
          hideInMenu: true
        },
        component: () => import('@/view/shipperOwner/index/shipperOwnerDetail.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'materialNum',
    component: Main,
    meta: {
      isCustom: true,
      icon: 'icon icon-menu_icon5',
      title: '物料备件数量',
      access: ['materialPurchase']
    },
    children: [
      {
        path: 'materialSingleNum',
        name: 'materialSingleNum',
        meta: {
          isCustom: true,
          icon: 'icon icon-menu_icon6',
          title: '单种采购量'
        },
        component: () => import('@/view/materialSingleNum/index.vue')
      },
      {
        path: 'materialShipNum',
        name: 'materialShipNum',
        meta: {
          icon: 'md-boat',
          title: '船舶采购量'
        },
        component: () => import('@/view/materialShipNum/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'configure',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '设置',
      access: ['materialPurchase']
    },
    children: [
      {
        path: 'dataConfigure',
        name: 'dataConfigure',
        meta: {
          icon: 'md-lock',
          title: '数据配置'
        },
        component: () => import('@/view/dataConfigure/index')
      },
      {
        path: 'trilateralOilPrice',
        name: 'trilateralOilPrice',
        meta: {
          icon: 'ios-pin',
          title: '三方价格'
        },
        component: () => import('@/view/trilateralOilPrice/index')
      },
      {
        path: 'areaConfigure',
        name: 'areaConfigure',
        meta: {
          icon: 'ios-pin',
          title: '区域配置'
        },
        component: () => import('@/view/areaConfigure/index')
      }
    ]
  },
  {
    path: '/',
    name: 'examModule',
    component: Main,
    meta: {
      icon: 'ios-albums-outline',
      title: '考试模块',
      access: ['examSystem']
    },
    children: [
      {
        path: 'memberManagement',
        name: 'memberManagement',
        meta: {
          icon: 'md-people',
          title: '成员管理'
        },
        component: () => import('@/view/examModule/memberManagement')
      },
      {
        path: 'questionBankList',
        name: 'questionBankList',
        meta: {
          icon: 'md-list',
          title: '题库列表'
        },
        component: () => import('@/view/examModule/questionBankList')
      },
      {
        path: 'questionBankClassify',
        name: 'questionBankClassify',
        meta: {
          icon: 'ios-folder-outline',
          title: '题库类别'
        },
        component: () => import('@/view/examModule/questionBankClassify/index.vue')
      },
      {
        path: 'jobCategory',
        name: 'jobCategory',
        meta: {
          icon: 'md-person',
          title: '归属分类'
        },
        component: () => import('@/view/examModule/jobCategory')
      }
    ]
  },
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import ('@/view/error-page/401.vue')
  },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import ('@/view/error-page/500.vue')
  }
];
