<template>
  <Card>
    <Tabs type="card" v-model="tabId" @on-click="tabChange">
      <TabPane label="待办">
        <search @searchResults='searchResults' :setSearch='remainSearchData'  @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
        <Table :data="dataList" :columns="curColumns"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </TabPane>
      <TabPane label="已办">
        <search @searchResults='searchResults' :setSearch='doneSearchData'  @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
        <Table :data="dataList" :columns="doneColumns"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </TabPane>
    </Tabs>
  </Card>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/performance'
import deptAPI from '@/api/jurisdictionManage/departManagement'

export default ({
  components: {
    search
  },
  data () {
    return {
      tabId: 0,
      total: 0,
      next_unified_account_id: '',
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        node_state: 0, // 处理状态（0待办；1已办）
        current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        belong_month: '',
        user_name: '', // 用戶
        is_finish: '', // 表单状态（0拟稿，1审核中，2完结）
        is_draft: 0 // 是否为起草人列表（1仅查询起草人相关数据；0查询审批相关数据）
      },
      remainSearchData: {
        user_name: {
          type: 'text',
          label: '姓名',
          selected: '',
          width: 135,
          value: '',
          isdisable: false,
          change: this.userNameChange
        },
        dept_id: {
          type: 'select',
          label: '部门',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          change: this.deptChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        },
        date: {
          type: 'month',
          label: '绩效月份',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        }
      },
      doneSearchData: {
        user_name: {
          type: 'text',
          label: '姓名',
          selected: '',
          width: 135,
          value: '',
          isdisable: false,
          change: this.userNameChange
        },
        dept_id: {
          type: 'select',
          label: '部门',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          change: this.deptChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        },
        date: {
          type: 'month',
          label: '绩效月份',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        },
        status: {
          type: 'select',
          label: '状态',
          selectData: [{
            value: 0,
            label: '拟稿'
          },
          {
            value: 1,
            label: '审核中'
          },
          {
            value: 2,
            label: '已完成'
          }],
          selected: '',
          placeholder: '请选择',
          change: this.statusChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        }
      },
      curColumns: [
        {
          title: '姓名',
          key: 'user_name',
          align: 'center'
        },
        {
          title: '部门',
          key: 'dept_name',
          align: 'center'
        },
        {
          title: '绩效月份',
          key: 'belong_month',
          align: 'center'
        },
        {
          title: '自评',
          key: 'self_evaluate_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '考核成绩',
          key: 'final_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '操作',
          key: 'date',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-color-wand',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleApprove(params.row)
                  }
                }
              }, '审批')
            ])
          }
        }
      ],
      doneColumns: [
        {
          title: '姓名',
          key: 'user_name',
          align: 'center'
        },
        {
          title: '部门',
          key: 'dept_name',
          align: 'center'
        },
        {
          title: '绩效月份',
          key: 'belong_month',
          align: 'center'
        },
        {
          title: '自评',
          key: 'self_evaluate_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '考核成绩',
          key: 'final_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '状态',
          key: 'node_state',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            let statusStr = ''
            if (params.row.is_finish === '0') statusStr = '拟稿'
            if (params.row.is_finish === '1') statusStr = '审核中'
            if (params.row.is_finish === '2') statusStr = '已完成'
            return h('div', {}, statusStr)
          }
        },
        {
          title: '操作',
          key: 'date',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }, '查看')
            ])
          }
        }
      ],
      dataList: [] // 列表
    }
  },
  methods: {
    // 获取列表数据
    getList () {
      API.queryPerformancePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.dataList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 获取部门列表数据
    getDeptList () {
      deptAPI.queryPerfPersonalDept().then(res => {
        if (res.data.Code === 10000) {
          this.remainSearchData.dept_id.selectData = this.doneSearchData.dept_id.selectData = res.data.Result.map(item => {
            return {
              value: item.dept_id,
              label: item.dept_name
            }
          })
        }
      })
    },
    // 审批
    handleApprove (item) {
      if (item.form_json && item.form_json.formType) {
        localStorage.setItem('formType', item.form_json.formType)
      }
      this.$router.push({
        name: 'performanceDetail',
        params: {
          id: 'approve&id=' + item.form_id
        }
      })
    },
    // 查看详情
    handleDetail (item) {
      if (item.form_json && item.form_json.formType) {
        localStorage.setItem('formType', item.form_json.formType)
      }
      this.$router.push({
        name: 'performanceDetail',
        params: {
          id: 'detail&id=' + item.form_id
        }
      })
    },
    tabChange () {
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1,
        node_state: this.tabId, // 处理状态（0待办；1已办）
        current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        belong_month: '',
        user_name: '', // 用戶
        is_finish: '', // 表单状态（0拟稿，1审核中，2完结）
        is_draft: 0 // 是否为起草人列表（1仅查询起草人相关数据；0查询审批相关数据）
      }
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.belong_month = e.key
      }
    },
    // 部门改变
    deptChange (e) {
      this.listQuery.dept_id = e.selected
    },
    statusChange (e) {
      this.listQuery.is_finish = e.selected
    },
    userNameChange (type, e) {
      this.listQuery.user_name = e.value
    },
    // 查询
    searchResults (e) {
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.belong_month = ''
      this.listQuery.is_finish = ''
      this.listQuery.user_name = ''
      this.listQuery.dept_id = ''
      this.remainSearchData.date.selected = ''
      this.doneSearchData.date.selected = ''
      this.doneSearchData.user_name.value = ''
      this.remainSearchData.user_name.value = ''
      this.remainSearchData.dept_id.selected = ''
      this.doneSearchData.dept_id.selected = ''
      this.doneSearchData.status.selected = ''
      this.getList()
    }
  },
  created () {
    let _sysUser = JSON.parse(localStorage.getItem('userData')).user_name
    let _flowObj = JSON.parse(localStorage.getItem('userFlow'))
    if (_flowObj.flowList && _flowObj.flowList.length > 0) {
      let _sysIdx = _flowObj.flowList.findIndex(item => item.user_name === _sysUser)
      if (_sysIdx < (_flowObj.flowList.length - 1)) {
        this.next_unified_account_id = _flowObj.flowList[_sysIdx + 1].unified_account_id
      } else {
        this.next_unified_account_id = ''
      }
    }
    this.getDeptList()
    this.getList()
  }
})
</script>
