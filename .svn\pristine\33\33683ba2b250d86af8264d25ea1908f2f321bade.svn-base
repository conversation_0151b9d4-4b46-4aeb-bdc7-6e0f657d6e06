import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询船舶支出 分页
export function queryImosShipCostPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/imos/ship/cost/queryImosShipCostPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询船舶支出 列表
export function queryImosShipCostList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/imos/ship/cost/queryImosShipCostList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增或修改详情
export function addOrUpdateImosShipCost (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/imos/ship/cost/addOrUpdateImosShipCost',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除
export function delImosShipCost (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/imos/ship/cost/delImosShipCost',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryImosShipCostPage,
  queryImosShipCostList,
  addOrUpdateImosShipCost,
  delImosShipCost
}