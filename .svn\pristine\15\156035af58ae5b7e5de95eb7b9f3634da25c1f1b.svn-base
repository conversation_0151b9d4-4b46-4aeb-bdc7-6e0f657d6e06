<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <!-- <div class="extra" slot="extra">
        <Button type="primary" @click="exportData" style="float: right;">导出</Button>
      </div> -->
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <trainDetailModal :modalData="modalData"></trainDetailModal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import trainDetailModal from './trainDetailModal'
import { queryTrainMemberPage } from '@/api/examSystem/trainingModule/memberManagement'
export default {
  components: {
    search,
    trainDetailModal
  },
  data () {
    return {
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        member_name: '',
        member_mobile: '',
        member_id: '',
        pageSize: 10,
        pageIndex: 1
      },
      setSearchData: {
        member_name: {
          type: 'text',
          label: '姓名',
          width: 180,
          value: '',
          isdisable: false
        },
        member_mobile: {
          type: 'text',
          label: '手机号码',
          width: 180,
          value: '',
          isdisable: false
        }
      },
      modalData: {
        modal: false,
        data: undefined
      },
      columns: [
        {
          title: '姓名',
          key: 'member_name',
          align: 'center'
        },
        {
          title: '电话',
          key: 'member_mobile',
          align: 'center'
        },
        {
          title: '报名次数',
          key: 'member_apply_num',
          align: 'center'
        },
        {
          title: '签到次数',
          key: 'member_sign_num',
          align: 'center'
        },
        {
          title: '考试次数',
          key: 'member_exam_num',
          align: 'center'
        },
        {
          title: '参评次数',
          key: 'ember_evaluate_num',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDetailModal(params.row)
                  }
                }
              }, '详情')
            ])
          }
        }
      ]
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      queryTrainMemberPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 查看详情
    handleDetailModal (row) {
      this.modalData = {
        modal: true,
        data: row
      }
    },
    // 导出
    exportData () {
      let data = Object.assign(this.listQuery, { template_name: '' })
      materialCompareExport(data).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.listQuery.member_id = e.member_id
      this.listQuery.member_name = e.member_name
      this.listQuery.member_mobile = e.member_mobile
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        member_id: '',
        member_name: '',
        member_mobile: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.member_name.value = ''
      this.setSearchData.member_mobile.value = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
