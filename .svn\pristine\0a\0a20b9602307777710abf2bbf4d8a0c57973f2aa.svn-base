import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 保存/修改 绩效表单
export function addOrUpdatePerfForm (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/form/addOrUpdatePerfForm',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 审批时间配置获取
export function queryPerformancePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/flow/history/queryPerfFlowWorksheetPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 提交流程
export function submitPerfForm (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/form/submitPerfForm',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取表单详情
export function getPerfFormInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/form/getPerfFormInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取表单流转历史
export function queryPerfFlowHistoryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/flow/history/queryPerfFlowHistoryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  addOrUpdatePerfForm,
  queryPerformancePage,
  submitPerfForm,
  getPerfFormInfo,
  queryPerfFlowHistoryList
}
