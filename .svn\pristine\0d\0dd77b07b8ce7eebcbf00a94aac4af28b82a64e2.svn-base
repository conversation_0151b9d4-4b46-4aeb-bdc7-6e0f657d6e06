<template>
  <div>
    <!-- 用户信息 -->
    <Card>
      <div class="user-con">
        <img src="../../../assets/images/header.png" />
        <div class="user-txt">
          <div class="user-txt-line">
            <span>用户</span>
            <span>{{ baseObj.seafarer_name }}</span>
          </div>
          <div class="user-txt-line">
            <span>{{ baseObj.status_name }}</span>
            <span>{{ shipperSkillObj.on_board_name || '-'}}</span>
            <span>{{ baseObj.crt_duty_name }}</span>
            <span v-if="shipperSkillObj.on_board_date && shipperSkillObj.on_board_date !== ''">({{ shipperSkillObj.on_board_date ? shipperSkillObj.on_board_date.split(' ')[0] : '-' }} ~ 至今)</span>
          </div>
        </div>
      </div>
    </Card>
    <!-- 基本信息 -->
    <Card class="detail-con">
      <div>
        <h3 class="card-title">基本信息</h3>
        <div class="detail-block">
          <Form :label-width="105" style="margin-bottom: 20px;">
            <Row>
              <Col span="6">
                <FormItem label="姓名">
                  <span>{{ baseObj.seafarer_name }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="出生日期">
                  <span>{{ baseObj.birthday ? baseObj.birthday.split(' ')[0] : '-' }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="入职时间">
                  <span>{{ baseObj.enter_company_date ? baseObj.enter_company_date.split(' ')[0] : '-' }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="身份证号">
                  <span>{{ baseObj.seafarer_id_no }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="6">
                <FormItem label="学校">
                  <span>{{ baseObj.school || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="学历">
                  <span>{{ baseObj.education_value || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="职务">
                  <span>{{ baseObj.crt_duty_name }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="任职时间">
                  <span>{{ Math.ceil(parseInt(baseObj.enter_company_months) / 12) }}年{{ parseInt(baseObj.enter_company_months) % 12 }}个月</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="6">
                <FormItem label="政治面貌">
                  <span>{{ baseObj.political_value || '-'  }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="籍贯">
                  <span>{{ baseObj.place_of_origin_prov_value }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="民族">
                  <span>{{ baseObj.nation }}</span>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="婚姻状况">
                  <span>{{ baseObj.marriage_status_value }}</span>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div>
          <h3 class="card-title">在船履历</h3>
          <Button class="edit_btn" :type="isEdit ? 'success' : 'primary'" @click="editClick">{{ isEdit ? '保存' : '编辑'}}</Button>
        </div>
        <div class="detail-block">
          <Form :label-width="150">
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 160px;">在船资历</div>
              </Col>
              <Col span="20">
                <FormItem label="小于3千吨（月）">
                  {{ serviceLevel0 }}
                </FormItem>
                <FormItem label="3千-1万总吨（月）">
                  {{ serviceLevel1 }}
                </FormItem>
                <FormItem label="1万-3万总吨（月）">
                  {{ serviceLevel2 }}
                </FormItem>
                <FormItem label="大于3万吨（月）">
                  {{ serviceLevel3 }}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 120px;">在船表现</div>
              </Col>
              <Col span="20">
                <FormItem label="管理能力">
                  <span v-if="isEdit">
                    <Input type='text' v-model='shipperSkillObj.management_ability'></Input>
                  </span>
                  <span v-else>{{ shipperSkillObj.management_ability || '-' }}</span>
                </FormItem>
                <FormItem label="工作技能">
                  <span v-if="isEdit">
                    <Input type='text' v-model='shipperSkillObj.working_skill'></Input>
                  </span>
                  <span v-else>{{ shipperSkillObj.working_skill || '-' }}</span>
                </FormItem>
                <FormItem label="是否服从调配">
                  <span v-if="isEdit">
                    <Select v-model="shipperSkillObj.allow_deploy" filterable label-in-value style="width: 55px;" @on-change="crewAllowChange">
                      <Option v-for="item in crewAllowList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                    </Select>
                  </span>
                  <span v-else>{{ shipperSkillObj.allow_deploy_name || '-'  }}</span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 80px;">服务年限</div>
              </Col>
              <Col span="20">
                <FormItem label="其他公司">
                  <span v-if="serviceCompanyList.length === 0">-</span>
                  <span v-for="(item, idx) in serviceCompanyList" :key="item.crt_duty_id">
                    <Button class="txt_btn" @click="detailClick('其他公司', idx)">{{ item.crt_duty_name }}({{ item.total_months }}个月)</Button>
                  </span>
                  <Button v-if="isEdit" class="add_btn" type="primary" @click="addClick('其他公司')">新增</Button>
                </FormItem>
                <FormItem label="本公司">
                  <span v-if="dutyList.length === 0">-</span>
                  <span v-for="(item, idx) in dutyList" :key="item.duty_id">
                    <Button class="txt_btn" @click="detailClick('本公司', idx)">{{ item.service_info }}</Button>
                  </span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 40px;">持有证书</div>
              </Col>
              <Col span="20">
                <FormItem :label-width="0">
                  <span v-if="certificateList.length === 0">-</span>
                  <span v-for="(item, idx) in certificateList" :key="item.certificate_key">
                    <Button class="txt_btn" @click="detailClick('持有证书', idx)">{{ item.certificate_name }}</Button>
                  </span>
                  <Button v-if="isEdit" class="add_btn" type="primary" @click="addClick('持有证书')">新增</Button>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 120px;">上条/往期船航次数量</div>
              </Col>
              <Col span="20">
                <FormItem :label-width="0" class="item-scroll">
                  <span v-if="voyageList.length === 0">-</span>
                  <span v-for="(item, idx) in voyageList" :key="item.ship_name">
                    <Button class="txt_btn" @click="detailClick('航次数量', idx)">{{ item.ship_name }}({{ item.voyage_num }}航次)</Button>
                  </span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="min-height: 120px;">离靠码头次数</div>
              </Col>
              <Col span="20">
                <FormItem :label-width="0" class="item-scroll">
                  <span v-if="wharfList.length === 0">-</span>
                  <span v-for="(item, idx) in wharfList" :key="item.wharf_id">
                    <Button class="txt_btn" @click="detailClick('码头次数', idx)">{{ item.wharf_name }}({{ item.wharf_num }}次)</Button>
                  </span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 40px;">外部检查（应对能力）</div>
              </Col>
              <Col span="20">
                <FormItem :label-width="0">
                  <span v-if="inspecList.length === 0">-</span>
                  <span v-for="(item, idx) in inspecList" :key="item.name_key">
                    <Button class="txt_btn" @click="detailClick('外部检查', idx)">{{ item.inspection_name }}({{ item.inspection_times }}次)</Button>
                  </span>
                  <Button v-if="isEdit" class="add_btn" type="primary" @click="addClick('外部检查')">新增</Button>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 40px;">获得荣誉</div>
              </Col>
              <Col span="20">
                <FormItem :label-width="0">
                  <span v-if="hornorList.length === 0">-</span>
                  <span v-for="(item, idx) in hornorList" :key="item.title">
                    <Button class="txt_btn" @click="detailClick('获得荣誉', idx)">{{ item.title_name }}</Button>
                  </span>
                  <Button v-if="isEdit" class="add_btn" type="primary" @click="addClick('获得荣誉')">新增</Button>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="4">
                <div class="block-head" style="height: 40px;">其他技能（爱好）</div>
              </Col>
              <Col span="20">
                <FormItem :label-width="0">
                  <span v-if="isEdit">
                    <Input type='text' v-model='shipperSkillObj.other_skills'></Input>
                  </span>
                  <span v-else>{{ shipperSkillObj.other_skills || '-' }}</span>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </Card>
    <DetailModal :modalData="modalObj" @modalDataBack="modalBackData"></DetailModal>
    <AddModal :modalData="addObj" @modalDataBack="modalBackData"></AddModal>
  </div>
</template>
<script>
import BasicAPI from '@/api/basicData'
import { getSeafarerInfo, querySeafarerExtrasList, updateSeafarerExtras, querySeafarerServiceAndDetail, queryCertificateAndDetail, queryInspectionAndDetail, queryHonorAndDetail } from '@/api/shipperOwner/index/index'
import AddModal from './addModal.vue'
import DetailModal from './detailModal.vue'

export default {
  components: {
    AddModal,
    DetailModal
  },
  data () {
    return {
      isBackData: false, // 是否回调标识
      isEdit: false, // 是否当前在编辑状态，默认否
      addObj: { // 新增数据绑定给予子组件
        modal: false, // 是否显示
        title: '', // 标题
        data: [] // 数据
      },
      modalObj: { // 详情数据绑定给子组件
        type: false, // 编辑状态
        modal: false, // 是否显示
        title: '', // 标题
        data: [] // 数据
      },
      detailId: '',
      baseObj: {}, // 列表带回船员基础数据
      shipperSkillObj: {}, // 船员技能
      dutyList: [], // 职务列表
      wharfList: [], // 港口列表
      voyageList: [], // 航次列表
      serviceCompanyList: [], // 其他公司服务年限列表
      certificateList: [], // 证书列表
      inspecList: [], // 外部检查列表
      hornorList: [], // 荣誉列表
      crewAllowList: [], // 是否服从调配 字典项数据
      serviceLevel0: '-', // 在船资历，小于3千
      serviceLevel1: '-', // 在船资历，3千-1万
      serviceLevel2: '-', // 在船资历，1万-3万
      serviceLevel3: '-' // 在船资历，大于3万
    }
  },
  created () {
    this.getList()
  },
  destroyed () {
    // sessionStorage.removeItem('shipperObj')
  },
  methods: {
    // 获取数据 初始
    getList () {
      this.detailId = this.$route.params.id
      this.baseObj = JSON.parse(sessionStorage.getItem('shipperObj'))
      if (!this.detailId || this.detailId === '') return
      this.$nextTick(() => {
        this.getBasicData()
        this.getShipperList()
        this.getVoyageList()
        this.getServiceCompany()
        this.getCertificateList()
        this.getInspecList()
        this.getHornorList()
      })
    },
    modalBackData () {
      this.isBackData = true
      this.updateBaseMess()
    },
    // 获取外部检查数据
    getInspecList () {
      queryInspectionAndDetail({
        seafarer_id: this.detailId
      }).then(res => {
        if (res.data.Code === 10000) {
          this.inspecList = res.data.Result
        }
      })
    },
    // 获取荣誉数据
    getHornorList () {
      queryHonorAndDetail({
        seafarer_id: this.detailId
      }).then(res => {
        if (res.data.Code === 10000) {
          this.hornorList = res.data.Result
        }
      })
    },
    // 获取船员证书信息
    getCertificateList () {
      queryCertificateAndDetail({
        seafarer_id: this.detailId
      }).then(res => {
        if (res.data.Code === 10000) {
          this.certificateList = res.data.Result
        }
      })
    },
    // 获取服务公司年限信息
    getServiceCompany () {
      querySeafarerServiceAndDetail({
        seafarer_id: this.detailId
      }).then(res => {
        if (res.data.Code === 10000) {
          this.serviceCompanyList = res.data.Result
        }
      })
    },
    // 获取船员基础数据
    getShipperList () {
      querySeafarerExtrasList({
        seafarer_id: this.detailId
      }).then(res => {
        if (res.data.Code === 10000) {
          this.shipperSkillObj = res.data.Result
        }
      })
    },
    // 船员关联航次信息 汇总
    getVoyageList () {
      getSeafarerInfo({
        seafarer_id: this.detailId
      }).then(res => {
        if (res.data.Code === 10000) {
          this.dutyList = res.data.dutyLength
          this.wharfList = res.data.nearByWharf
          this.voyageList = res.data.voyageInfo
          if (res.data.serviceSeniority && res.data.serviceSeniority.length > 0) {
            res.data.serviceSeniority.forEach(item => {
              switch (item.service_level) {
                case '0':
                  this.serviceLevel0 = item.service_info
                  break
                case '1':
                  this.serviceLevel1 = item.service_info
                  break
                case '2':
                  this.serviceLevel2 = item.service_info
                  break
                case '3':
                  this.serviceLevel3 = item.service_info
                  break
                default:
                  break
              }
            })
          }
        }
      })
    },
    // 新增功能
    addClick (addStr) {
      switch (addStr) {
        case '其他公司':
          this.addObj = {
            modal: true,
            title: '其他公司服务年限',
            data: [
              {
                crt_duty_id: '',
                crt_duty_name: '',
                company_name: '',
                ship_name: '',
                months: '',
                bak: ''
              }
            ]
          }
          break
        case '持有证书':
          this.addObj = {
            modal: true,
            title: '证书资质',
            data: [{
              certificate_key: '',
              certificate_name: '',
              acquisition_time: '',
              validity_start: '',
              validity_end: '',
              bak: ''
            }]
          }
          break
        case '外部检查':
          this.addObj = {
            modal: true,
            title: '外部检查详情',
            data: [{
              ship_id: '',
              ship_name: '',
              name_key: '',
              inspection_name: '',
              inspection_time: '',
              evaluate_key: '',
              evaluate_name: '',
              bak: ''
            }]
          }
          break
        case '获得荣誉':
          this.addObj = {
            modal: true,
            title: '荣誉详情',
            data: [{
              ship_id: '',
              ship_name: '',
              voyage_no: '',
              get_time: '',
              title_name: '',
              bak: ''
            }]
          }
          break
        default:
          break
      }
    },
    crewAllowChange (obj) {
      this.shipperSkillObj.allow_deploy_name = obj.label
    },
    // 编辑状态点击切换
    editClick () {
      if (this.isEdit) { // 保存数据
        this.updateBaseMess()
      }
      this.isEdit = !this.isEdit
    },
    updateBaseMess () {
      updateSeafarerExtras({
        seafarer_id: this.detailId,
        management_ability: this.shipperSkillObj.management_ability,
        working_skill: this.shipperSkillObj.working_skill,
        allow_deploy: this.shipperSkillObj.allow_deploy,
        other_skills: this.shipperSkillObj.other_skills
      }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
        if (this.isBackData) {
          this.getList()
          this.isBackData = false
        }
      })
    },
    // 获取基础数据
    getBasicData () {
      BasicAPI.queryDictCacheList({
        dic_code: 'unCrewAllowDeploy'
      }).then(res => {
        if (res.data.Code === 10000) {
          this.crewAllowList = res.data.Result
        }
      })
    },
    // 详情点击展示
    detailClick (detailStr, idx) {
      switch (detailStr) {
        case '其他公司':
          this.modalObj = {
            type: this.isEdit,
            modal: true,
            title: '其他公司服务年限',
            data: this.serviceCompanyList[idx].details
          }
          break
        case '本公司':
          this.modalObj = {
            type: this.isEdit,
            modal: true,
            title: '本公司服务年限',
            data: this.dutyList[idx].duty_details
          }
          break
        case '持有证书':
          this.modalObj = {
            type: this.isEdit,
            modal: true,
            title: '证书资质',
            data: this.certificateList[idx].details
          }
          break
        case '航次数量':
          if (this.voyageList[idx].voyage_details.length > 0) {
            this.modalObj = {
              type: this.isEdit,
              modal: true,
              title: '往期航线详情',
              data: this.voyageList[idx].voyage_details
            }
          } else {
            this.$Message.warning('暂无信息')
          }
          break
        case '码头次数':
          this.modalObj = {
            type: this.isEdit,
            modal: true,
            title: '离靠码头详情',
            data: this.wharfList[idx].near_by_details
          }
          break
        case '外部检查':
          this.modalObj = {
            type: this.isEdit,
            modal: true,
            title: '外部检查详情',
            data: this.inspecList[idx].details
          }
          break
        case '获得荣誉':
          this.modalObj = {
            type: this.isEdit,
            modal: true,
            title: '荣誉详情',
            data: this.hornorList[idx].details
          }
          break
        default:
          break
      }
    }
  }
}
</script>
<style lang="less">
  .user-con {
    display: flex;
    align-items: center;
    justify-items: center;
    .user-txt {
      margin-left: 20px;
      .user-txt-line {
        height: 30px;
        line-height: 30px;
        span {
          margin-right: 10px;
        }
      }
    }
  }
  .detail-con {
    margin-top: 20px;
    .detail-block {
      margin-top: 10px;
      .block-head {
        display: flex;
        min-height: 40px;
        max-height: 160px;
        background-color: #EFF8FF;
        border-top: 1px solid #DAE1ED;
        border-right: 1px solid #DAE1ED;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
      .detail-other-block {
        font-size: 12px;
        font-weight: 500;
        color: #4A4A4A;
        margin-bottom: 10px;
      }
    }
    .ivu-form {
      border-left: 1px solid #DAE1ED;
      border-bottom: 1px solid #DAE1ED;
    }
    .detail-port-type {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }
    .detail-name {
      font-size: 16px;
      color: #333;
      font-weight: 600;
      margin-left: 20px;
    }
    .detail-time {
      font-size: 16px;
      color: #333;
      font-weight: 400;
      margin-left: 20px;
    }
    .ivu-form-item {
      margin-bottom: 0;
      height: 40px;
      line-height: 40px;
    }
    .ivu-form-item-label {
      padding: 0 10px;
      text-align: left;
      font-weight: 600;
      border: 1px solid #DAE1ED;
      border-bottom: none;
      border-left: none;
      background-color: #EFF8FF;
      height: 40px;
      line-height: 40px;
    }
    .ivu-form-item-content {
      border: 1px solid #DAE1ED;
      border-left: none;
      border-bottom: none;
      padding: 0 10px;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .item-scroll .ivu-form-item-content {
    overflow-y: auto;
    text-overflow: clip;
    white-space: break-spaces;
    height: 120px;
  }
  .txt_btn {
    margin-right: 10px;
    height: 30px;
    line-height: 30px;
    padding: 0 5px;
    cursor: pointer;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  .add_btn {
    position: absolute;
    right: 10px;
    margin-top: 3px;
  }
  .edit_btn {
    position: absolute;
    right: 18px;
    margin-top: -30px;
  }
</style>
