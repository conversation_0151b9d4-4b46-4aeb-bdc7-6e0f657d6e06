<template>
    <div class="agent-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="title-container">
                <div class="logo-icon">
                    <img src="@/assets/images/xingzai.png" alt="AI Logo">
                </div>
                <h1 class="page-title">
                    <span class="title-text">AI-兴仔</span>
                    <span class="title-glow"></span>
                    <div class="title-particles">
                        <span class="particle" v-for="n in 6" :key="n"></span>
                    </div>
                </h1>
            </div>
            <p class="page-subtitle">
                <span class="subtitle-text">AI智能体助手平台</span>
                <!-- <span class="typing-cursor">|</span> -->
            </p>
        </div>

        <!-- 搜索栏 -->
        <div class="search-section">
            <div class="search-container">
                <div class="search-icon">
                    <Icon type="ios-search" size="20" />
                </div>
                <Input v-model="searchKeyword" placeholder="搜索智能体..." class="search-input" @on-change="handleSearch" />
                <div class="search-glow"></div>
            </div>
        </div>

        <!-- 智能体卡片列表 -->
        <div class="agent-grid">
            <Card v-for="agent in filteredAgents" :key="agent.id" class="agent-card"
                :class="{ 'featured': agent.featured }" @click.native="selectAgent(agent)">
                <div class="card-header">
                    <div class="agent-icon">
                        <Icon :type="agent.icon" :size="32" :color="agent.iconColor" />
                    </div>
                    <div class="agent-info">
                        <h3 class="agent-name">{{ agent.name }}</h3>
                        <p class="agent-category">{{ agent.category }}</p>
                    </div>
                    <div class="agent-status">
                        <Badge :status="agent.status === 'active' ? 'success' : 'default'" />
                    </div>
                </div>

                <div class="card-content">
                    <p class="agent-description">{{ agent.description }}</p>

                    <div class="agent-features">
                        <Tag v-for="feature in agent.features" :key="feature" :color="getFeatureColor(feature)"
                            class="feature-tag">
                            {{ feature }}
                        </Tag>
                    </div>
                </div>


            </Card>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="totalAgents > pageSize">
            <Page :total="totalAgents" :page-size="pageSize" :current="currentPage" @on-change="handlePageChange"
                show-elevator show-sizer show-total />
        </div>
    </div>
</template>

<script>
export default {
  name: 'agent',
  data() {
    return {
      searchKeyword: '',
      currentPage: 1,
      pageSize: 12,
      totalAgents: 0,
      agents: [
        {
          id: 1,
          name: '船舶营运助手',
          category: '船舶营运',
          description: '提供船舶营运管理服务，包括船舶信息查询、船期预测、AIS数据分析等功能，提升船舶营运效率，降低成本。',
          icon: 'ios-boat',
          iconColor: '#2d8cf0',
          status: 'active',
          featured: true,
          features: ['船舶信息', '船期预测', 'AIS数据'],
          apiKey: 'app-ilgQfXl1nsyXd8AsoQL6H6cd'
        },
        {
          id: 3,
          name: 'AI知识库',
          category: '知识库',
          description: '智能知识库管理助手，提供文档检索、知识问答、内容管理等功能，帮助用户快速获取和管理企业知识资产，提升工作效率。',
          icon: 'ios-book',
          iconColor: '#ff9900',
          status: 'active',
          featured: true,
          features: ['文档检索', '知识问答', '内容管理'],
          apiKey: 'app-NcbUs5JIDBPagsTSQ0mLjVY9'
        },
      ]
    }
  },
  computed: {
    filteredAgents() {
      let filtered = this.agents

      if (this.searchKeyword) {
        filtered = filtered.filter(agent =>
          agent.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
                    agent.description.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
                    agent.category.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }

      this.totalAgents = filtered.length

      // 分页处理
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return filtered.slice(start, end)
    }
  },
  methods: {
    handleSearch() {
      this.currentPage = 1
    },
    handlePageChange(page) {
      this.currentPage = page
    },
    selectAgent(agent) {
      localStorage.setItem('agentApiKey', agent.apiKey)
      this.$router.push({
        name: 'agentChat'
      })
    },

    getFeatureColor(feature) {
      const colors = ['blue', 'green', 'orange', 'red', 'purple']
      const index = feature.length % colors.length
      return colors[index]
    }
  },
  mounted() {
    let menuList = JSON.parse(this.$store.state.user.menuList)
    this.agents = menuList.filter(item => item.href === 'agent')[0].children.map(item=>{
      let component = JSON.parse(item.component)
      return {
        id: item.id,
        name: item.name,
        category: component.category,
        description: component.description,
        icon: component.icon,
        iconColor: component.iconColor,
        status: 'active',
        featured: component.featured,
        features: component.features.split(','),
        apiKey: item.href
      }
    })
  }
}
</script>

<style scoped>
/* 确保页面可以正常滚动 */
.agent-container {
    padding: 24px;
    /* background: linear-gradient(145deg, #ffffff 0%, #c5e1fa 100%); */
    background: url('../../assets/images/agent_bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 页面头部 */
.page-header {
    text-align: center;
    /* margin-bottom: 40px; */
    color: #2c3e50;
    position: relative;
    /* backdrop-filter: blur(5px);
  background: rgba(255, 255, 255, 0.1); */
    border-radius: 20px;
    padding: 30px 20px;
    margin: 0 auto 20px auto;
    max-width: 600px;
    /* box-shadow: 0 8px 32px rgba(255, 255, 255, 0.2); */
}

/* 标题容器 */
.title-container {
    margin-top: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    /* margin-bottom: 20px; */
}

/* Logo */
.logo-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
    animation: logoFloat 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: logoShine 4s ease-in-out infinite;
}

.logo-icon img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    z-index: 1;
    position: relative;
    filter: brightness(1.1);
}

.page-title {
    font-size: 64px;
    font-weight: bold;
    margin: 0;
    position: relative;
    display: inline-block;
    font-family: 'Arial', 'Helvetica', sans-serif;
    letter-spacing: 3px;
}

.title-text {
    background: linear-gradient(45deg, #000000, #1e3a8a, #000000);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 4s ease-in-out infinite;
    position: relative;
    z-index: 2;
    text-shadow: 0 0 30px rgba(30, 58, 138, 0.6);
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

.title-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    opacity: 0.2;
    filter: blur(25px);
    animation: pulse 2s ease-in-out infinite alternate;
    z-index: 1;
}

.title-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 5px;
    height: 5px;
    background: #4a90e2;
    border-radius: 50%;
    opacity: 0;
    animation: float 3s ease-in-out infinite;
    box-shadow: 0 0 10px rgb(74, 145, 226);
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 0.5s;
}

.particle:nth-child(3) {
    left: 80%;
    animation-delay: 1s;
}

.particle:nth-child(4) {
    left: 90%;
    animation-delay: 1.5s;
}

.particle:nth-child(5) {
    left: 30%;
    animation-delay: 2s;
}

.particle:nth-child(6) {
    left: 70%;
    animation-delay: 2.5s;
}

.page-subtitle {
    font-size: 20px;
    margin: 0;
    color: #000927;
    position: relative;
    font-weight: 400;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.subtitle-text {
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.5s forwards;
}

.typing-cursor {
    color: #000927;
    animation: blink 1s infinite;
    font-weight: normal;
    margin-left: 2px;
}

/* 动画定义 */
@keyframes logoFloat {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
        box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
    }

    50% {
        transform: translateY(-10px) rotate(5deg);
        box-shadow: 0 15px 35px rgba(74, 144, 226, 0.4);
    }
}

@keyframes logoShine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    50% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }

    100% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes pulse {
    0% {
        opacity: 0.2;
        transform: scale(1);
    }

    100% {
        opacity: 0.4;
        transform: scale(1.05);
    }
}

@keyframes float {

    0%,
    100% {
        opacity: 0;
        transform: translateY(25px) scale(0);
    }

    50% {
        opacity: 0.8;
        transform: translateY(-25px) scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0;
    }
}

@keyframes searchPulse {

    0%,
    100% {
        opacity: 0.15;
        transform: scale(1);
    }

    50% {
        opacity: 0.25;
        transform: scale(1.02);
    }
}

/* 搜索区域 */
.search-section {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;
}

.search-container {
    position: relative;
    width: 450px;
    max-width: 90%;
}

.search-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: #4a90e2;
    z-index: 3;
    transition: all 0.3s ease;
}

.search-input {
    width: 100%;
    height: 50px;
    position: relative;
    z-index: 2;
}

.search-input>>>.ivu-input {
    height: 50px;
    border-radius: 25px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(30, 58, 138, 0.1), 0 4px 16px rgba(255, 255, 255, 0.3);
    font-size: 16px;
    padding-left: 50px;
    padding-right: 20px;
    color: #1e40af;
    transition: all 0.3s ease;
}

.search-input>>>.ivu-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 8px 32px rgba(30, 58, 138, 0.2), 0 0 0 3px rgba(59, 130, 246, 0.15);
    background: rgba(255, 255, 255, 0.95);
}

.search-input>>>.ivu-input::placeholder {
    color: #7f8c8d;
    font-weight: 300;
}

.search-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 25px;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    opacity: 0;
    filter: blur(20px);
    transition: opacity 0.3s ease;
    z-index: 1;
}

.search-container:hover .search-glow {
    opacity: 0.1;
}

.search-container:focus-within .search-glow {
    opacity: 0.15;
    animation: searchPulse 2s ease-in-out infinite;
}

.search-container:focus-within .search-icon {
    color: #357abd;
    transform: translateY(-50%) scale(1.1);
}

/* 卡片网格 */
.agent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 24px;
    margin: 0 200px 32px 200px;
    padding: 0 10px;
}

/* 智能体卡片 */
.agent-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 32px rgba(30, 58, 138, 0.08), 0 4px 16px rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(12px);
}

.agent-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 40px rgba(30, 58, 138, 0.12), 0 8px 24px rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.95);
}

.agent-card.featured {
    /* border: 2px solid #ff9500; */
    position: relative;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15), 0 4px 16px rgba(255, 255, 255, 0.25);
}

.agent-card.featured::before {
    content: '推荐';
    position: absolute;
    top: 12px;
    right: 12px;
    background: #ff9500;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    z-index: 1;
}

/* 卡片头部 */
.card-header {
    display: flex;
    align-items: center;
    padding: 16px 20px 12px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.agent-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
}

.agent-info {
    flex: 1;
    min-width: 0;
}

.agent-name {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #2c3e50;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.agent-category {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
}

.agent-status {
    flex-shrink: 0;
}

/* 卡片内容 */
.card-content {
    padding: 12px 20px;
}

.agent-description {
    font-size: 14px;
    line-height: 1.5;
    color: #5a6c7d;
    margin: 0 0 12px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.agent-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.feature-tag {
    font-size: 12px;
    border-radius: 12px;
}



/* 分页 */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}

.pagination-wrapper>>>.ivu-page {
    background: white;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .agent-container {
        padding: 16px 4%;
        min-height: auto;
        height: auto;
    }

    .page-header {
        margin-bottom: 24px;
        padding: 20px 15px;
        border-radius: 15px;
    }

    .title-container {
        flex-direction: column;
        gap: 15px;
    }

    .logo-icon {
        width: 60px;
        height: 60px;
    }

    .logo-icon img {
        width: 45px;
        height: 45px;
    }

    .page-title {
        font-size: 42px;
        letter-spacing: 2px;
    }

    .page-subtitle {
        font-size: 18px;
    }

    .title-particles {
        display: none;
        /* 移动端隐藏粒子效果 */
    }

    .search-section {
        margin-bottom: 24px;
    }

    .search-container {
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
    }

    .search-input>>>.ivu-input {
        height: 45px;
        font-size: 15px;
    }

    .agent-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin: 0 20px 24px 20px;
    }

    .card-header {
        padding: 12px 16px 10px 16px;
    }

    .card-content {
        padding: 10px 16px;
    }
}

@media (max-width: 480px) {
    .agent-container {
        padding: 12px 3%;
        min-height: 100vh;
        height: auto;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .page-header {
        margin-bottom: 20px;
        padding: 15px 10px;
        border-radius: 12px;
    }

    .title-container {
        flex-direction: column;
        gap: 12px;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
    }

    .logo-icon img {
        width: 35px;
        height: 35px;
    }

    .page-title {
        font-size: 32px;
        letter-spacing: 1px;
    }

    .page-subtitle {
        font-size: 16px;
    }

    .search-section {
        margin-bottom: 20px;
    }

    .search-container {
        width: 100%;
    }

    .search-input>>>.ivu-input {
        height: 42px;
        font-size: 14px;
        border-radius: 21px;
    }

    .search-icon {
        left: 15px;
    }

    .search-input>>>.ivu-input {
        padding-left: 45px;
    }

    .agent-grid {
        margin: 0 10px 20px 10px;
    }

    .agent-name {
        font-size: 16px;
    }

    .agent-description {
        font-size: 13px;
    }

    .pagination-wrapper {
        padding: 16px 0;
    }

    .title-glow {
        filter: blur(15px);
        /* 减少模糊效果 */
    }
}
</style>
