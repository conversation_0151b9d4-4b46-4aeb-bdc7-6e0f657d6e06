<template>
  <Layout style="height: 100%" class="main">
    <!-- 左侧菜单导航 -->
    <Sider v-show="this.$route.name !== 'home'" hide-trigger collapsible :width="200" :collapsed-width="64" v-model="collapsed" class="left-sider" :style="{overflow: 'hidden'}">
      <side-menu ref="sideMenu" :active-name="$route.name" :collapsed="collapsed" @on-select="turnToPage" :menu-list="menuList">
        <div class="logo-con">
          <img v-show="!collapsed" :src="logo" key="max-logo" />
          <span>兴通海运</span>
          <img v-show="collapsed" :src="minLogo" key="min-logo" />
        </div>
      </side-menu>
    </Sider>
    <Layout>
      <Header class="header-con">
        <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
          <div class="header-logo">
            <img class="head-logo" :src="logo" alt="">
            <span>兴通海运</span>
          </div>
          <div>
            <Tooltip placement="bottom">
              <Button icon="ios-log-out" size="large" type="text" @click="quitLogin()"></Button>
              <div slot="content">
                <p>退出登录</p>
              </div>
            </Tooltip>
          </div>
          <Button type="default" @click="manageLogin">后台管理</Button>
          <user :message-unread-count="unreadCount" :user-avatar="userAvatar"/>
        </header-bar>
      </Header>
      <Content class="main-content-con" :style="this.$route.name === 'home' ? 'display: inline;' : ''">
        <div class="topdate" v-show="userDataSyncDate">
          <DatePicker type="daterange" split-panels placeholder="选择日期" @on-change="changeSyncDate" style="width: 200px;"></DatePicker>
          <Button type="primary" @click="dataToSyncData" style="margin-left: 10px;">数据同步</Button>
        </div>
        <Layout class="main-layout-con">
          <Content class="content-wrapper" :class="this.$route.name === 'home' ? 'content-home' : ''">
            <keep-alive :include="cacheList">
              <router-view/>
            </keep-alive>
            <ABackTop :height="100" :bottom="80" :right="50" container=".content-wrapper"></ABackTop>
          </Content>
        </Layout>
      </Content>
    </Layout>
  </Layout>
</template>
<script>
import Cookies from 'js-cookie'
import SideMenu from './components/side-menu'
import HeaderBar from './components/header-bar'
// import TagsNav from './components/tags-nav'
import User from './components/user'
import ABackTop from './components/a-back-top'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import { getNewTagList, routeEqual } from '@/libs/util'
import routers from '@/router/routers'
import appCode from '@/assets/images/app.png'
import logo from '@/assets/images/logo-boat.png'
import minLogo from '@/assets/images/logo-min.png'
import maxLogo from '@/assets/images/logo.png'
import { dataToSync } from '@/api/basicData'
import './main.less'
export default {
  name: 'Main',
  components: {
    SideMenu,
    HeaderBar,
    // TagsNav,
    User,
    ABackTop
  },
  data () {
    return {
      userDataSyncDate: false,
      collapsed: false,
      searchText: '',
      appCode,
      logo,
      minLogo,
      maxLogo,
      dataToDataParam: {
        auditdate_begin: '', // 审核日期开始
        auditdate_end: '' // 审核日期结束
      }
    }
  },
  computed: {
    ...mapGetters([
      'errorCount'
    ]),
    tagNavList () {
      return this.$store.state.app.tagNavList
    },
    tagRouter () {
      return this.$store.state.app.tagRouter
    },
    userAvatar () {
      return this.$store.state.user.avatarImgPath
    },
    cacheList () {
      const list = ['ParentView', ...this.tagNavList.length ? this.tagNavList.filter(item => !(item.meta && item.meta.notCache)).map(item => item.name) : []]
      return list
    },
    menuList () {
      return this.$store.getters.menuList
    },
    hasReadErrorPage () {
      return this.$store.state.app.hasReadErrorPage
    },
    unreadCount () {
      return this.$store.state.user.unreadCount
    }
  },
  methods: {
    ...mapMutations([
      'setBreadCrumb',
      'setTagNavList',
      'addTag',
      // 'setLocal',
      'setHomeRoute',
      'closeTag'
    ]),
    ...mapActions([
      'handleLogin',
      'handleLogOut',
      'getUnreadMessageCount'
    ]),
    turnToPage (route) {
      let { name, params, query } = {}
      if (typeof route === 'string') name = route
      else {
        name = route.name
        params = route.params
        query = route.query
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        window.open(name.split('_')[1])
        return
      }
      this.$nextTick(() => {
        this.$router.push({
          name,
          params,
          query
        })
      })
    },
    // 开始查询
    searchVoyage (val) {
      if (val === '') return
      API.queryWaybillNumber({ number_no: val }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.Result.length === 0) {
            this.$Notice.warning({
              title: '无此单号',
              desc: '查无此单号信息,请确认单号信息是否正确!'
            })
          } else {
            localStorage.setItem('voyageObj', JSON.stringify(res.data.Result[0]))
            this.searchText = ''
            this.$router.push({
              name: 'searchInDetail',
              params: {
                id: val
              }
            })
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleCollapsedChange (state) {
      this.collapsed = state
      // this.$store.commit('setCollapsed', state)
      // localStorage.setItem('collapsed', state)
    },
    handleCloseTag (res, type, route) {
      if (type !== 'others') {
        if (type === 'all') {
          // this.turnToPage('home')
          this.turnToPage(this.$config.homeName)
        } else {
          if (routeEqual(this.$route, route)) {
            this.closeTag(route)
          }
        }
      }
      this.setTagNavList(res)
    },
    handleClick (item) {
      this.turnToPage(item)
    },
    // 退出登录
    quitLogin () {
      this.handleLogOut().then(() => {
        this.$router.push({
          name: 'login'
        })
      })
    },
    // 选择数据同步日期
    changeSyncDate (val) {
      this.dataToDataParam = {
        auditdate_begin: val[0],
        auditdate_end: val[1]
      }
    },
    // 数据同步
    dataToSyncData () {
      const msg = this.$Message.loading({
        content: '数据同步中...',
        duration: 0
      })
      let data = this.dataToDataParam.auditdate_begin === '' ? {back_days: '-7'} : this.dataToDataParam
      dataToSync(data).then(res => {
        setTimeout(msg)
        if (res.data.Code === 10000) {
          this.$Message.success('同步成功')
        } else {
          this.$Message.error('同步失败')
        }
      })
    },
    // 跳转后台管理
    manageLogin () {
      console.log(3)
    }
  },
  watch: {
    '$route' (newRoute) {
      const { name, query, params, meta } = newRoute
      this.addTag({
        route: { name, query, params, meta },
        type: 'push'
      })
      this.setBreadCrumb(newRoute)
      this.setTagNavList(getNewTagList(this.tagNavList, newRoute))
      this.$refs.sideMenu.updateOpenName(newRoute.name)
      this.userDataSyncDate = localStorage.getItem('userDataId') === 'a5f4cc75010e44f4b8ec175b1db284ff' && this.$route.path !== '/home' && Cookies.get('access') === 'materialPurchase'
    }
  },
  mounted () {
    // this.collapsed = Boolean(localStorage.collapsed)
    /**
     * @description 初始化设置面包屑导航和标签导航
     */
    this.setTagNavList()
    this.setHomeRoute(routers)
    const { name, params, query, meta } = this.$route
    this.addTag({
      route: { name, params, query, meta }
    })
    this.setBreadCrumb(this.$route)
    // 设置初始语言
    // this.setLocal(this.$i18n.locale)
    // 如果当前打开页面不在标签栏中，跳到homeName页
    if (!this.tagNavList.find(item => item.name === this.$route.name)) {
      this.$router.push({
        name: this.$config.homeName
        // name: 'home'
      })
    }
    this.userDataSyncDate = localStorage.getItem('userDataId') === 'a5f4cc75010e44f4b8ec175b1db284ff' && this.$route.path !== '/home' && Cookies.get('access') === 'materialPurchase'
    // 获取未读消息条数
    this.getUnreadMessageCount()
  }
}
</script>
