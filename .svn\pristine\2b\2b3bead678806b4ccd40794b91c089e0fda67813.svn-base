<template>
  <div>
    <Row>
      <Col span="8"><!-- 配置列表 -->
        <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list" class="alignTable" highlight-row @on-row-click="clickHandleOption"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listQuery.pageIndex"
        :total="total" prev-text="< 上一页" next-text="下一页 >"
        @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </Col>
      <Col offset="1" span="15"><!-- 选项列表 -->
        <div>
          <Button class="add_btn" @click="addOption">新增</Button>
        </div>
        <Table border :loading="optionLoading" ref="selection" :columns="optionColumns" :data="optionList" no-data-text="暂无数据" class="alignTable"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.optionQuery.pageSize" :current.sync="optionQuery.pageIndex"
        :total="optionTotal" prev-text="< 上一页" next-text="下一页 >"
        @on-change='optionHandleCurrentChange' @on-page-size-change='optionHandleSizeChange'/>
      </Col>
    </Row>
    <Modal v-model="modalData.modal" :title="modalData.title" width="300" :mask-closable="false" @on-visible-change="modalShow" @on-ok="handleModalOk">
      <div><Input type='text' v-model='modalData.data.entry_name'></Input></div>
      <div style="margin-top: 15px;">
        <Select v-model="modalData.data.is_available" style="width:100px">
          <Option v-for="item in visibleList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </div>
    </Modal>
  </div>
</template>
<script>
import BasicAPI from '@/api/basicData'
// unCrewHonorTitle 荣誉  /  unCrewInspectionName  外部检查   /    unCrewEvaluate  评价   子：选项名称   主： 配置名称
export default ({
  data () {
    return {
      modalData: {
        modal: false,
        type: '',
        title: '',
        data: {}
      },
      visibleList: [
        {
          value: '1',
          label: '是'
        },
        {
          value: '0',
          label: '否'
        }
      ],
      listLoading: false,
      total: 0,
      curIndex: 0, // 默认首行高亮
      columns: [
        {
          title: '配置名称',
          key: 'dic_name',
          align: 'center'
        }
      ],
      list: [],
      listQuery: {
        dic_code_like: 'unCrew',
        pageSize: 10,
        pageIndex: 1
      },
      optionQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      optionLoading: false,
      optionTotal: 0,
      optionColumns: [
        {
          title: '选项名称',
          key: 'entry_name',
          align: 'center'
        },
        {
          title: '是否可用',
          key: 'is_available',
          align: 'center',
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.is_available === '1'
              },
              on: {
                'on-change': () => {
                  params.row.is_available = params.row.is_available === '0' ? '1' : '0'
                  this.updateDic(params.row)
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              style: {
                margin: '4px'
              },
              props: {
                icon: 'md-brush',
                size: 'small'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.modalData = {
                    modal: true,
                    type: 'modify',
                    title: '选项名称修改',
                    data: params.row
                  }
                }
              }
            }, '修改')
          }
        }
      ],
      optionList: []
    }
  },
  methods: {
    // 配置项列表
    getList () {
      this.listLoading = true
      BasicAPI.queryDictPage(this.listQuery).then(res => {
        this.listLoading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
          if (this.list.length < 0) return
          this.list[this.curIndex]._highlight = true
          this.getOptionList()
        }
      })
    },
    // 选项列表
    getOptionList () {
      this.optionLoading = true
      Object.assign(this.optionQuery, {
        dic_code: this.list[this.curIndex].dic_code
      })
      BasicAPI.queryDictEntryPage(this.optionQuery).then(res => {
        this.optionLoading = false
        if (res.data.Code === 10000) {
          this.optionList = res.data.Result
          this.optionTotal = res.data.Total
        }
      })
    },
    addOption () {
      this.modalData = {
        modal: true,
        type: 'add',
        title: '选项名称新增',
        data: []
      }
    },
    // 修改字典项
    updateDic (item) {
      BasicAPI.updateDictEntryShort({
        dic_entry_id: item.dic_entry_id,
        dic_code: item.dic_code,
        entry_name: item.entry_name,
        is_available: item.is_available
      }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    clickHandleOption (row, index) {
      this.resetOptionParam()
      this.curIndex = index
      this.getOptionList()
    },
    // 重置入参
    resetOptionParam () {
      this.optionList = []
      this.optionQuery = {
        pageSize: 10,
        pageIndex: 1
      }
    },
    handleModalOk () {
      if (this.modalData.type === 'modify') { // 修改
        BasicAPI.updateDictEntryShort({
          dic_entry_id: this.modalData.data.dic_entry_id,
          dic_code: this.modalData.data.dic_code,
          entry_name: this.modalData.data.entry_name,
          is_available: this.modalData.data.is_available
        }).then(res => {
          this.modalData.modal = false
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.resetOptionParam()
            this.getOptionList()
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
      if (this.modalData.type === 'add') { // 新增
        BasicAPI.addDictEntryShort({
          dic_code: this.list[this.curIndex].dic_code,
          entry_name: this.modalData.data.entry_name,
          is_available: this.modalData.data.is_available
        }).then(res => {
          this.modalData.modal = false
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.resetOptionParam()
            this.getOptionList()
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    // 弹窗显隐
    modalShow (val) {
      if (val) {

      }
    },
    // 公司列表页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 公司列表分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 成员列表页面跳转
    optionHandleSizeChange (val) {
      this.optionQuery.pageSize = val
      this.getOptionList()
    },
    // 成员列表分页跳转
    optionHandleCurrentChange (val) {
      this.optionQuery.pageIndex = val
      this.getOptionList()
    }
  },
  created () {
    this.getList()
  }
})
</script>
<style scoped>
  .add_btn {
    position: absolute;
    z-index: 999;
    right: 10px;
    top: 5px;
  }
  .menu_tab {
    height: 50px;
    line-height: 50px;
    background: #fff;
    border-radius: 8px;
    padding: 0 10px;
    text-align: center;
    color: #333;
    font-size: 18px;
    font-weight: bold;
    margin-top: 15px;
    border: 1px solid #333;
    cursor: pointer;
  }
  .menu_tab.cur {
    background: #189BD5;
    color: #fff;
    border: none;
  }
</style>
