import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 用户管理--获取瀛海用户下拉框
export function queryAllStaffList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/staff/queryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--获取一体化用户列表
export function queryUnifiedAccountList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/queryUnifiedAccountList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--添加一体化用户
export function addUnifiedAccount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/addUnifiedAccount',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--一体化用户修改
export function updateUnifiedAccount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/updateUnifiedAccount',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--一体化用户删除
export function delUnifiedAccount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/delUnifiedAccount',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--一体化用户密码重置
export function resetUnifiedAccountPw (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/updateUnifiedAccountPassword',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取海运管家用户账号列表 - 不分页
export function queryAccountList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/passport/queryAccountList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取海运管家用户账号列表 - 分页
export function queryAccountPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/passport/queryAccountPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--部门成员列表 分页(限制部门id)
export function queryPostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/queryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--部门成员列表 分页(不限制部门id-2022.03.31废弃)
export function getMultiInfosPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/staff/getMultiInfosPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--部门成员列表 分页(不限制部门id-2022.03.31废弃)
export function queryUnifiedAccountPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/queryUnifiedAccountPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--添加部门成员
export function addPostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/add',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--修改部门成员
export function updatePostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 用户管理--删除部门成员
export function delPostStaffPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/staff/post/delete',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
