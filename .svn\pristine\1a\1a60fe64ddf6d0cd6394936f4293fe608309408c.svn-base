import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 船员信息列表-不分页
export function querySeafarerInfoList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/info/querySeafarerInfoList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船员合同列表 分页
export function querySeafarerContractPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/querySeafarerContractPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 预警文件数量
export function queryFileDirectoryOverdueCount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/queryFileDirectoryOverdueCount',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船员合同列表 不分页
export function querySeafarerContractList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/querySeafarerContractList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// token免认证查询接口
export function querySeafarerContractObject (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/querySeafarerContractObject',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船员职务列表
export function querySeafarerDutyList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/querySeafarerDutyList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增船员合同
export function addSeafarerContract (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/addSeafarerContract',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改船员合同
export function updateSeafarerContract (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/updateSeafarerContract',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 废除船员合同
export function delSeafarerContract (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/delSeafarerContract',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船员合同归档
export function updateSeafarerContractAuditStatus (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/updateSeafarerContractAuditStatus',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改 审核状态
export function changeSeafarerContractAuditStatus (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/changeSeafarerContractAuditStatus',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 手动触发船员覆盖
export function SeafarerInfoSyncService (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/info/SeafarerInfoSyncService',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 短信推送内容
export function pushMsg (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/pushMsg',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 短信验证码获取
export function getSeafarerContractMsg (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/getSeafarerContractMsg',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 校验短信验证码
export function checkSeafarerContractMsg (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/checkSeafarerContractMsg',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 手动上传合同附件 并归档
export function updateSeafarerContractReplaceFile (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/updateSeafarerContractReplaceFile',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批压缩下载
export function downloadFileDirectory (data) {
  let header = {...config.ajaxHeader, ...{
    'Content-Type': 'application/json;application/octet-stream'
  }}
  return axios.request({
    url: '/seafarer/contract/downloadSeafarerContracts',
    method: 'get',
    headers: header,
    responseType: 'blob',
    params: data
  })
}

// 契约锁 http请求中转
export function requestTransferStation (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/contract/requestTransferStation',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  querySeafarerInfoList,
  querySeafarerContractPage,
  queryFileDirectoryOverdueCount,
  querySeafarerContractList,
  querySeafarerContractObject,
  querySeafarerDutyList,
  addSeafarerContract,
  updateSeafarerContract,
  delSeafarerContract,
  updateSeafarerContractAuditStatus,
  changeSeafarerContractAuditStatus,
  SeafarerInfoSyncService,
  pushMsg,
  getSeafarerContractMsg,
  checkSeafarerContractMsg,
  updateSeafarerContractReplaceFile,
  downloadFileDirectory,
  requestTransferStation
}