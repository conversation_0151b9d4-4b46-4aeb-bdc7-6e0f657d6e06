<template>
  <Row>
    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>{{ spinTxt }}</div>
    </Spin>
    <Col span="3" style="margin-left: -10px;">
      <Card>
        <div v-for="(item,idx) in shipList" class="ship_btn_area" :style="curShip === idx ? 'background: #ebf7ff' : ''" @click="shipClick(idx)">
          <span>{{ item.name }}</span>
          <Icon class="ship_btn_close" size="20" type="md-close-circle" @click.stop="delShip(item)" />
        </div>
      </Card>
      <Button class="add_ship_btn" long type="primary" @click="addShip">添加船舶</Button>
    </Col>
    <Col style="margin-left: 10px;" span="21">
      <Card>
        <div class="btn_area">
          <div class="back_btn">
            <Button style="margin-left: -10px;" v-if="this.breadNameList.length > 1" type="text" icon="md-arrow-back" @click="backParent">返回</Button>
            <span class="bread-span" v-for="(item, idx) in breadNameList" :key="'bread' + idx" :style="(breadNameList.length > 1 && idx < breadNameList.length - 1) ? 'color: #2d8cf0; cursor: pointer;' : ''" @click="breadClick(idx)">
              <Icon v-if="idx > 0" type="ios-arrow-forward"/>
              <h4>{{ item.name }}</h4>
            </span>
          </div>
          <Button :type="isModify ? 'primary' : 'success'" icon="ios-brush-outline" @click="multiModify">{{ isModify ? '保存' : '编辑' }}</Button>
          <Button type="warning" @click="addFolder" :disabled="shipList.length === 0" icon="ios-folder-outline">添加文件夹</Button>
          <Button icon="ios-cloud-upload-outline" type="primary" @click="multiUpload">文件上传</Button>
          <Button :disabled="selectList.length === 0" type="error" icon="ios-close-circle" @click="delMultiFile">批量删除</Button>
          <Button :disabled="selectList.length === 0" icon="ios-cloud-download-outline" @click="downLoadFile">下载</Button>
        </div>
        <Table ref="tableSelection" border row-key="id" :loading="isLoading" :columns="columnsList" :data="tableList" @on-selection-change="selectChange"></Table> <!-- @on-row-click="tableRowClick"-->
      </Card>
    </Col>
    <CertificateModify :modalData="modifyModal" @updateBack="modifyBack"></CertificateModify>
    <input
      ref="fileInput"
      type="file"
      style="display: none;"
      @change="handleFileChange"
    />
    <input
      ref="multiFile"
      type="file"
      multiple
      style="display: none;"
      @change="handleMultiFileChange"
    />
  </Row>
</template>

<script>
import CertificateModify from './certificateModify'
import fileUpload from './fileUpload.vue'
import API from '@/api/erpSys/certificate'
import CryptoJS from 'crypto-js'
import axios from 'axios'
import { getToken, getShipCompanyId } from '@/libs/util'
import config from '@/config'
const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro

export default ({
  components: {
    CertificateModify,
    fileUpload
  },
  data () {
    return {
      secretKey: 'UNIFIED_Xt603209',
      spinShow: false,
      spinTxt: '加载中',
      isLoading: false,
      formDataList: [],
      breadNameList: [], // 面包屑列表
      isModify: false, // 是否批量编辑
      curShip: 0,
      curModifyFileObj: {}, // 当前在修改的单个文件信息
      curFolderObj: {}, // 当前所在的文件夹 信息
      shipList: [], // 船舶列表
      modifyModal: { // 文件编辑弹窗 参数
        modal: false,
        data: {},
        title: '文件编辑'
      },
      selectList: [], // 被选中的内容
      shipQuery: {
        parent_id: null,
        path: null,
        is_root: '1',
        type: 'directory'
      },
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      total: 0,
      columnsList: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          key: 'name',
          title: '名称',
          align: 'left',
          sortable: true,
          sortMethod: (a, b, type) => {
            if (type === 'asc') {
              return a > b ? -1 : 1
            } else {
              return a > b ? 1 : -1
            }
          },
          render: (h, params) => {
            let _that = this
            if (this.isModify || params.row.isNameModify) { // 打开编辑状态
              return h('span',{
                  style: {
                    display: 'inline-flex',
                    'align-items': 'center',
                    width: '100%'
                  }
                },
                [
                  h('Icon', {
                  props: {
                    type: 'ios-folder-open-outline',
                    size: '16'
                  },
                  style: {
                    display: params.row.type === 'directory' ? 'inline-block' : 'none',
                    marginRight: '5px'
                  }
                }),
                h('Input', {
                  props: {
                    value: params.row.name,
                    autofocus: true,
                    placeholder: '请输入名称'
                  },
                  style: {
                    width: '100%'
                  },
                  on: {
                    input: (val) => {
                      _that.tableList[params.row._index].name = val
                    }
                  }
                })
              ])
            } else {
              return h('div', {
                style: {
                  cursor: params.row.type === 'directory' ? 'pointer' : ''
                },
                on: {
                  click: async (event) => {
                    this.folderClick(params.row)
                  }
                }
              }, [ h('Icon', {
                  props: {
                    type: 'ios-folder-open-outline',
                    size: '16'
                  },
                  style: {
                    display: params.row.type === 'directory' ? 'inline-block' : 'none',
                    marginRight: '5px'
                  }
                }),
                h('span', params.row.name)
              ])
            }
          }
        },
        {
          key: 'name_cn',
          title: '中文名称',
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (this.isModify) { // 打开编辑状态
              return h('span',{
                  style: {
                    display: 'inline-flex',
                    'align-items': 'center',
                    width: '100%'
                  }
                },
                [
                h('Input', {
                  props: {
                    value: params.row.name_cn,
                    autofocus: true,
                    placeholder: '请输入中文名称'
                  },
                  style: {
                    width: '100%'
                  },
                  on: {
                    input: (val) => {
                      _that.tableList[params.row._index].name_cn = val
                    }
                  }
                })
              ])
            } else {
              return h('div', {}, params.row.name_cn)
            }
          }
        },
        {
          key: 'issue_date',
          title: '签发日期',
          width: 150,
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (this.isModify) {
              return h('DatePicker', {
                props: {
                  value: params.row.issue_date,
                  type: 'date',
                  format: 'yyyy-MM-dd'
                },
                style: {
                  display: params.row.type === 'directory' ? 'none' : 'inline-block',
                  width: '100%'
                },
                on: {
                  'on-change': (date) => {
                    _that.tableList[params.row._index].issue_date = date // 处理日期更改
                  }
                }
              })
            } else {
              return h('span', {}, params.row.issue_date || '--')
            }
          }
        },
        {
          key: 'expiration_date',
          title: '到期时间',
          width: 150,
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (this.isModify) {
              return h('DatePicker', {
                props: {
                  value: params.row.expiration_date,
                  type: 'date',
                  format: 'yyyy-MM-dd'
                },
                style: {
                  display: params.row.type === 'directory' ? 'none' : 'inline-block',
                  width: '100%'
                },
                on: {
                  'on-change': (date) => {
                    _that.tableList[params.row._index].expiration_date = date // 处理日期更改
                  }
                }
              })
            } else {
              return h('span', {}, params.row.expiration_date || '--')
            }
          }
        },
        {
          key: 'status',
          title: '预警状态',
          width: 200,
          align: 'center',
          render: (h, params) => {
            return h('span', {
              style: {
                padding: '1px 10px',
                borderRadius: '15px',
                color: '#fff',
                background: this.getStatusColor(params.row.days_overdue)
              }
            }, params.row.status || '--')
          }
        },
        {
          key: 'remarks',
          title: '备注',
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (this.isModify) { // 打开编辑状态
              return h('span',{
                  style: {
                    display: 'inline-flex',
                    'align-items': 'center',
                    width: '100%'
                  }
                },
                [
                h('Input', {
                  props: {
                    value: params.row.remarks,
                    autofocus: true,
                    placeholder: '请输入备注信息'
                  },
                  style: {
                    width: '100%'
                  },
                  on: {
                    input: (val) => {
                      _that.tableList[params.row._index].remarks = val
                    }
                  }
                })
              ])
            } else {
              return h('div', {}, params.row.remarks)
            }
          }
        },
        {
          key: '',
          title: '操作',
          align: 'left',
          width: 300,
          render: (h, params) => {
            return h('div', [
            h('Button', {
                props: {
                  icon: 'md-eye',
                  size: 'small',
                  disabled: this.isModify
                },
                style: {
                  margin: '0 5px',
                  display: params.row.type === 'file' ? 'inline-block' : 'none'
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.curModifyFileObj = params.row
                    this.handleView(params.row)
                  }
                }
              }, '预览'),
              h('Button', {
                props: {
                  icon: 'md-brush',
                  size: 'small',
                  disabled: this.isModify
                },
                style: {
                  margin: '0 5px',
                  display: params.row.type === 'file' ? 'inline-block' : 'none'
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.curModifyFileObj = params.row
                    this.handleEditModal(params.row)
                  }
                }
              }, params.row.type === 'file' ? '修改附件' : '重命名'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small',
                  disabled: this.isModify
                },
                on: {
                  click: (event) => {
                    event.stopPropagation()
                    this.handleDelete(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],
      tableList: []
    }
  },
  methods: {
    getList (item) { // 获取列表数据
      this.isLoading = true
      Object.assign(this.listQuery, {
        parent_id: item.file_directory_id
      })
      API.queryFileDirectoryList(this.listQuery).then(res => {
        this.isLoading = false
        if (res.data.Code === 10000) {
          this.tableList = res.data.Result
        }
      })
    },
    getShipList () { // 获取船舶信息（根目录文件夹）
      API.queryFileDirectoryList(this.shipQuery).then(res => {
        if (res.data.Code === 10000) {
          this.shipList = res.data.Result
          if (this.shipList.length > 0) {
            this.curFolderObj = this.shipList[this.curShip]
            this.breadNameList.push({
              file_directory_id: this.curFolderObj.file_directory_id,
              name: this.curFolderObj.name
            })
            this.getList(this.curFolderObj)
          }
        }
      })
    },
    async handleFileChange (event) { // 替换单个文件
      const file = event.target.files[0]
      const formData = new FormData()
      const token = getToken()
      if (file.type !== undefined) {
        formData.append('token', token)
        formData.append('file_directory_id', this.curModifyFileObj.file_directory_id)
        formData.append('file_hash', this.curModifyFileObj.file_hash)
        formData.append('file', file)
      }
      this.spinShow = true
      this.spinTxt = '正在上传中…'
      const config = {
        onUploadProgress: progressEvent => {
          let progress = parseFloat(progressEvent.progress) * 100
          let percent = progress.toFixed(2)
          this.spinTxt = '已上传' + percent + '%'
          if (progress === 100) {
            this.spinTxt = '等待服务器解析中……'
          }
        }
      }
      const postUrl = baseUrl + 'file/directory/replaceSingleFile'
      await axios.post(postUrl, formData, config).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.isModify = true
          this.getList(this.curFolderObj)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
      // API.replaceSingleFile(formData).then(res => {
      //   this.curModifyFileObj = {} // 拉取后置空 防止指向出错
      //   if (res.data.Code === 10000) {
      //     this.spinShow = false
      //     this.$Message.success(res.data.Message)
      //     this.getList(this.curFolderObj)
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    breadClick (idx) { // 面包屑点击事件
      if (idx !== this.breadNameList.length - 1) {
        this.curFolderObj = this.breadNameList[idx]
        let curBread = this.breadNameList.findIndex(item => item.file_directory_id === this.breadNameList[idx].file_directory_id)
        if (curBread !== -1) {
          this.breadNameList.splice(curBread + 1)
        }
        this.getList(this.breadNameList[idx])
      }
    },
    backParent () { // 后退至上一个文件夹
      this.tableList = []
      if (this.breadNameList.length > 1) {
        this.breadNameList.pop()
        let _param = this.breadNameList[this.breadNameList.length - 1]
        this.getList(_param)
      }
    },
    multiModify () { // 文件名及到期时间批量编辑
      this.isModify = !this.isModify
      if (!this.isModify && this.tableList.length > 0) {
        let _tableList = this.tableList.map(item => {
          return {
            file_directory_id: item.file_directory_id,
            name: item.name,
            name_cn: item.name_cn,
            issue_date: item.issue_date,
            expiration_date: item.expiration_date,
            remarks: item.remarks
          }
        })
        let _param = {
          detailJson: JSON.stringify(_tableList)
        }
        this.spinShow = true
        this.spinTxt = '正在上传'
        API.updateFileDirectoryBatch(_param).then(res => {
          this.spinShow = false
          if (res.data.Code === 10000) {
            this.getList(this.curFolderObj)
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    async handleMultiFileChange (event) { // 批量文件上传
      const files = event.target.files
      const token = getToken()
      const formData = new FormData()
      const uploadChange = (progressEvent) => {
        console.log(progressEvent)
      }
      Array.from(files).forEach(file => {
        if (file.type !== undefined) {
          formData.append('token', token)
          formData.append('parent_id', this.curFolderObj.file_directory_id)
          formData.append('path', this.curFolderObj.path)
          formData.append('type', 'file')
          formData.append('files', file)
        }
      })
      this.spinShow = true
      this.spinTxt = '正在上传中…'
      const config = {
        onUploadProgress: progressEvent => {
          let progress = parseFloat(progressEvent.progress) * 100
          let percent = progress.toFixed(2)
          this.spinTxt = '已上传' + percent + '%'
          if (progress === 100) {
            this.spinTxt = '等待服务器解析中……'
          }
        }
      }
      const postUrl = baseUrl + 'file/directory/addFileDirectory'
      await axios.post(postUrl, formData, config).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.isModify = true
          this.getList(this.curFolderObj)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
      // API.fileUpload(formData, config).then(res => {
      //   this.spinShow = false
      //   if (res.data.Code === 10000) {
      //     this.isModify = true
      //     this.getList(this.curFolderObj)
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    multiUpload () {
      this.$refs.multiFile.value = ''
      this.$refs.multiFile.click()
    },
    handleView (row) { // 文件预览
      try {
        const encryptedData = CryptoJS.enc.Base64.parse(row.wpsUrl)
        const key = CryptoJS.enc.Utf8.parse(this.secretKey)

        const decrypted = CryptoJS.AES.decrypt(
          { ciphertext: encryptedData },
          key,
          { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
        )
        const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
        let imgType = ['bmp','jpg','jpeg','png','tif','gif','pcx','tga','exif','fpx','svg','psd','cdr','pcd','dxf','ufo','eps','ai','raw','WMF','webp','avif','apng']
        if (imgType.includes(row.file_type.split('.')[1])) {
          this.fileType = 'img'
          const imgData = CryptoJS.enc.Base64.parse(row.url)
          const key = CryptoJS.enc.Utf8.parse(this.secretKey)

          const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: imgData },
            key,
            { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
          )
          const imgUrl = decrypted.toString(CryptoJS.enc.Utf8)
          window.open(imgUrl, '_blank')
        } else {
          sessionStorage.setItem('wpsUrl', decryptedText)
          sessionStorage.setItem('token', getToken())
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        }
      } catch (err) {
        console.log('出错了： ' + err)
      }
    },
    handleEditModal (row) { // 编辑 文件夹和附件 单个修改
      if (row.type === 'directory') { // 修改文件夹名称 打开弹窗
        // this.modifyModal.modal = true
        // this.modifyModal.data = row
        Object.assign(this.tableList[row._index], {
          isNameModify: true
        })
        setTimeout(() => {
          this.$forceUpdate()
        }, 100)
      } else { // 修改附件调起本地文件上传
        this.$refs.fileInput.value = ''
        this.$refs.fileInput.click()
      }
    },
    handleDelete (row) { // 删除文件
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除文件？</p>',
        loading: true,
        onOk: () => {
          this.loading = false
          let _param = {
            file_directory_id: row.file_directory_id,
            path: row.path,
            name: row.name,
            type: row.type
          }
          API.delFileDirectory(_param).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList(this.curFolderObj)
            } else {
              this.$Message.error(res.data.Message)
            }
          })
          this.$Modal.remove()
        }
      })
    },
    handleAddFile (row) { // 表格内部添加二级文件夹
      this.$Modal.confirm({
        render: (h) => {
          return h('Input', {
            props: {
              value: this.value,
              autofocus: true,
              placeholder: '请输入文件夹名'
            },
            on: {
              input: (val) => {
                this.value = val
              }
            }
          })
        },
        onOk: () => { // 树状文件夹 待完善
          if (this.tableList[row._index].hasOwnProperty('children')) { // 如果已经有children数据，直接push
            this.tableList[row._index].children.push({
              file_name: this.value,
              type: 'directory'
            })
          } else {
            Object.assign(this.tableList[row._index], {
              children: [
                {
                  file_name: this.value,
                  type: 'directory'
                }
              ]
            })
          }
          this.value = ''
        },
        onCancel: () => {
          this.value = ''
        }
      })
    },
    handleUploadFile (row) { // 文件夹内上传文件
      
    },
    modifyBack () { // 文件名更新回调
      this.getList(this.curFolderObj)
    },
    shipClick (idx) { // 船舶点击
      this.curShip = idx
      this.curFolderObj = this.shipList[idx]
      this.breadNameList = [] // 先把列表置空再Push
      this.breadNameList.push({
        file_directory_id: this.curFolderObj.file_directory_id,
        name: this.curFolderObj.name,
        path: this.curFolderObj.path
      })
      this.getList(this.shipList[idx])
    },
    addShip () { // 添加船舶
      this.$Modal.confirm({
        render: (h) => {
          return h('Input', {
            props: {
              value: this.value,
              autofocus: true,
              placeholder: '请输入船舶名称'
            },
            on: {
              input: (val) => {
                this.value = val
              }
            }
          })
        },
        title: '添加船舶',
        onOk: () => {
          let _param = {
            parent_id: null,
            name: this.value,
            path: null,
            type: 'directory'
          }
          API.addFileDirectory(_param).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.breadNameList = []
              this.getShipList()
              this.value = ''
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        },
        onCancel: () => {
          this.value = ''
        }
      })
    },
    delShip (item) { // 删除船舶
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条船舶下所有数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          let _param = {
            file_directory_id: item.file_directory_id,
            path: null,
            name: item.name,
            type: item.type
          }
          API.delFileDirectory(_param).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.breadNameList = []
              this.curShip = 0
              this.getShipList()
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    addFolder () { // 添加文件夹
      this.$Modal.confirm({
        render: (h) => {
          return h('Input', {
            props: {
              value: this.value,
              autofocus: true,
              placeholder: '请输入文件夹名'
            },
            on: {
              input: (val) => {
                this.value = val
              }
            }
          })
        },
        title: '新增文件夹',
        onOk: () => {
          let _param = {
            parent_id: this.curFolderObj.file_directory_id,
            name: this.value,
            path: this.curFolderObj.path,
            type: 'directory'
          }
          API.addFileDirectory(_param).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.curShip = 0
              this.getList(this.curFolderObj)
            } else {
              this.$Message.error(res.data.Message)
            }
          })
          this.value = ''
        },
        onCancel: () => {
          this.value = ''
        }
      })
    },
    folderClick (row) { // 文件夹点击
      if (row.type === 'file') return
      this.tableList = []
      this.curFolderObj = row
      this.breadNameList.push({
        file_directory_id: this.curFolderObj.file_directory_id,
        name: this.curFolderObj.name
      })
      this.getList(row)
    },
    getStatusColor (days_overdue) {
      let colorStr = ''
      if (days_overdue !== '' && parseFloat(days_overdue) < -30) { // 未超期
        colorStr = '#19be6b'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= -30 && parseFloat(days_overdue) < 0) { // 30天内预警
        colorStr = '#ffa502'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= 0) { // 超期预警
        colorStr = '#e74c3c'
      }
      return colorStr
    },
    delMultiFile () { // 批量删除
      if (this.selectList.length > 0) {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>确定要删除选中数据，删除后将无法恢复？</p>',
          loading: true,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            let idList = this.selectList.map(item => item.file_directory_id)
            let _param = {
              file_directory_ids: idList.join()
            }
            this.spinShow = true
            this.spinTxt = '正在删除'
            API.deleteFileDirectories(_param).then(res => {
              this.spinShow = false
              this.$Modal.remove()
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.getList(this.curFolderObj)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        })
      }
    },
    downLoadFile () { // 下载附件
      if (this.selectList.length > 0) {
        let idList = this.selectList.map(item => item.file_directory_id)
        let _param = {
          file_directory_ids: idList.join(),
          path_like: this.selectList[0].path
        }
        this.spinShow = true
        this.spinTxt = '正在下载'
        API.downloadFileDirectory(_param).then(res => {
          if (res.status === 200) {
            this.spinShow = false
            const link = document.createElement('a')
            const name = this.curFolderObj.name
            try {
              const blob = new Blob([res.data], { type: 'application/zip' })
              const url = window.URL || window.webkitURL || window.moxURL

              link.href = url.createObjectURL(blob)
              link.setAttribute('download', name)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              url.revokeObjectURL(link.href)
              this.$refs.tableSelection.selectAll(false)
            } catch (e) {
              console.log('下载出错', e)
            }
          } else {
            console.log(res)
          }
        })
      }
    },
    selectChange (arr) { // 选中
      this.selectList = arr
    }
  },
  created () {
    this.getShipList()
  }
})
</script>
<style scoped>
  .ship_btn_area {
    cursor: pointer;
    background: #f7f7f7;
    padding: 10px 8px;
    margin-bottom: 10px;
    border-radius: 5px;
  }
  .ship_btn_close {
    cursor: pointer;
    float: right;
  }
  .ship_btn_close:hover {
    color: #2d8cf0;
  }
  .add_ship_btn {
    margin-top: 10px;
  }
  .btn_area {
    text-align: right;
    margin-bottom: 15px;
  }
  .back_btn {
    display: inline-flex;
    align-items: center;
    position: absolute;
    left: 10px;
  }
  .btn_area button {
    margin-left: 10px;
  }
  .bread-span {
    display: inline-flex;
    align-items: center;
  }
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>