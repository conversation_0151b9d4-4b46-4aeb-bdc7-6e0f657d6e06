<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='searchData'  @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 12px;">
      <div class="performance_title">{{ searchMonth }}绩效</div>
      <Row :gutter="48">
        <Col span="8">
          <Row class="list_box">
            <Col span="6" class="list_title_area">
              <div>总人数：{{ performanceList.accountStatMap ? performanceList.accountStatMap.total_num : 0 }}人</div>
              <div>已完成：{{ performanceList.accountStatMap ? performanceList.accountStatMap.finished_sum : 0 }}人</div>
              <div>未完成：{{ performanceList.accountStatMap ? performanceList.accountStatMap.unfinished_sum : 0}}人</div>
            </Col>
            <Col span="18">
              <Annular style="height: 200px;" :value="pieData1" :color="transColor" :center="pieCenter" text="绩效完成率"></Annular>
            </Col>
          </Row>
        </Col>
        <Col span="8">
          <Row class="list_box">
            <Col span="6" class="list_title_area">
              <div v-for="(item, idx) in performanceList.leveArray" :key="idx">
                {{ item.evaluate_level }}：{{ item.account_sum }}人
              </div>
            </Col>
            <Col span="18">
              <Annular style="height: 200px;" :value="pieData2" :color="transColor" :center="pieCenter" text="绩效占比"></Annular>
            </Col>
          </Row>
        </Col>
        <Col span="8">
          <div class="list_box">
            <div v-for="(item, idx) in performanceList.highestArray" :key="idx">
              <span :class="rankClass(idx)">{{ idx + 1 }}</span>
              <span :class="rankTitle(idx)">{{ item.user_name }}：{{ item.re_evaluate_score }}分</span>
              <span class="dept_rank">{{ item.dept_name }}</span>
            </div>
            <div v-show="!performanceList.highestArray || performanceList.highestArray.length === 0" class="data-none">暂无数据</div>
          </div>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import { Annular } from '_c/charts'
import { queryPerfStatInfo, queryDepartManageList } from '@/api/jurisdictionManage/departManagement'
import { getSysTime } from '@/api/basicData'

export default ({
  components: {
    search,
    Annular
  },
  data () {
    return {
      listQuery: {
        belong_month: '',
        dept_id: ''
      },
      searchMonth: '',
      preDate: '', // 默认前一个月时间
      performanceList: {},
      pieData1: [],
      pieData2: [],
      pieCenter: ['50%', '55%'],
      transColor: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3', '#5B8FF9', '#BDD2FD'],
      searchData: {
        date: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        },
        dept_id: {
          type: 'select',
          label: '部门',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          change: this.deptChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        }
      }
    }
  },
  methods: {
    getList () {
      queryPerfStatInfo(this.listQuery).then(res => {
        this.pieData1 = []
        this.pieData2 = []
        if (res.data.Code === 10000) {
          this.performanceList = res.data
          this.getMonthName()
          // this.searchMonth = this.listQuery.belong_month
          this.pieData1.push({
            value: res.data.accountStatMap.unfinished_sum,
            name: '未完成'
          }, {
            value: res.data.accountStatMap.finished_sum,
            name: '已完成'
          })
          this.pieData2 = res.data.leveArray.map(item => {
            return {
              value: item.account_sum,
              name: item.evaluate_level
            }
          })
        }
      })
    },
    // 获取部门列表数据
    getDeptList () {
      queryDepartManageList().then(res => {
        if (res.data.Code === 10000) {
          this.searchData.dept_id.selectData = res.data.Result.map(item => {
            return {
              value: item.dept_id,
              label: item.dept_name
            }
          })
        }
      })
    },
    // 部门改变
    deptChange (e) {
      this.listQuery.dept_id = e.selected
    },
    getMonthName () {
      let _monthNum = parseInt(this.listQuery.belong_month.substring(5, 7))
      switch (_monthNum) {
        case 1:
          this.searchMonth = '一月'
          break
        case 2:
          this.searchMonth = '二月'
          break
        case 3:
          this.searchMonth = '三月'
          break
        case 4:
          this.searchMonth = '四月'
          break
        case 5:
          this.searchMonth = '五月'
          break
        case 6:
          this.searchMonth = '六月'
          break
        case 7:
          this.searchMonth = '七月'
          break
        case 8:
          this.searchMonth = '八月'
          break
        case 9:
          this.searchMonth = '九月'
          break
        case 10:
          this.searchMonth = '十月'
          break
        case 11:
          this.searchMonth = '十一月'
          break
        case 12:
          this.searchMonth = '十二月'
          break
      }
    },
    rankClass (idx) {
      if (idx === 0) {
        return 'rank_back first_rank'
      } else if (idx === 1) {
        return 'rank_back second_rank'
      } else if (idx === 2) {
        return 'rank_back third_rank'
      } else {
        return 'rank_back other_rank'
      }
    },
    rankTitle (idx) {
      if (idx === 0) {
        return 'rank_title first_rank_title'
      } else if (idx === 1) {
        return 'rank_title second_rank_title'
      } else if (idx === 2) {
        return 'rank_title third_rank_title'
      } else {
        return 'rank_title other_rank_title'
      }
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.belong_month = e.key
      }
    },
    // 查询
    searchResults (e) {
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.belong_month = this.preDate
      this.listQuery.dept_id = ''
      this.searchData.date.selected = this.preDate
      this.searchData.dept_id.selected = ''
      this.getList()
    }
  },
  created () {
    this.getDeptList()
    getSysTime().then(res => {
      if (res.data.Code === 10000) {
        let _curMonth = res.data.now_date.substring(0, 7)
        let _monthStr = _curMonth.substring(5, 7)
        let _monthNum = parseInt(_monthStr)
        let _preMonth = ''
        if (_monthNum !== 1) {
          let _preMonthNum = _monthNum - 1
          _preMonth = _preMonthNum > 10 ? '' + _preMonthNum : '0' + _preMonthNum
        } else {
          _preMonth = '12'
        }
        let _preDate = _curMonth.substring(0, 5) + _preMonth
        this.preDate = _preDate
        this.searchData.date.selected = _preDate
        this.listQuery.belong_month = _preDate
        this.getList()
      }
    })
  }
})
</script>
<style>
.list_box {
  position: relative;
  height: 220px;
  border: 2px solid #6E6E6E88;
  padding: 5px 0;
}
.performance_title {
  font-size: 20px;
  font-weight: bold;
  height: 50px;
  line-height: 50px;
}
.list_title_area {
  padding: 15px 10px;
}
.list_title_area div {
  margin: 10px 0;
  font-size: 12px;
  color: #000;
}
.rank_back {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 1px solid #fff;
  text-align: center;
  line-height: 19px;
  border-radius: 20px;
  color: #fff;
  box-shadow: 2px 2px 5px #888;
  margin-right: 10px;
  margin: 10px 5px;
}
.rank_title {
  margin: 0 10px 0 0;
}
.dept_rank {
  position: absolute;
  color: #5191BE;
  right: 10px;
  margin-top: 10px;

}
.first_rank {
  background: #C32C2A;
}
.first_rank_title {
  color: #C32C2A;
}
.second_rank {
  background: #E4A045;
}
.second_rank_title {
  color: #E4A045;;
}
.third_rank {
  background: #B0EE49;
}
.third_rank_title {
  color: #B0EE49;
}
.other_rank {
  background: #aaa;
}
.other_rank_title {
  color: #aaa;
}
.data-none {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #516B91;
  font-size: 18px;
  font-weight: bold;
}
</style>
