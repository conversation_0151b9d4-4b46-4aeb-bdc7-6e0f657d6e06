<template>
  <div>
    <Modal v-model="modalData.modal" :title="modalData.title" width="660" @on-visible-change="modalVisible" :mask-closable="false" ok-text="保存" cancel-text="关闭" @on-ok="saveRemarker">
      <Input v-model="modalData.dept_remark" type="textarea" placeholder="请输入加减分说明" :autosize="{minRows: 8, maxRows: 15}"></Input>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/jurisdictionManage/deptProcess'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {

    }
  },
  methods: {
    modalVisible (val) {

    },
    saveRemarker () {
      API.updatePerfDeptFlowRemark({
        dept_id: this.modalData.dept_id,
        dept_remark: this.modalData.dept_remark
      }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.$emit('remarkBackData')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  }
}
</script>
