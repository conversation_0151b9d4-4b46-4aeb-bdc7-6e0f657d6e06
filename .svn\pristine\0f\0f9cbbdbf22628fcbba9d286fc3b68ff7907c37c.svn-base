<template>
  <Drawer title="成绩查询" v-model="modalData.modal" :data="modalData.data" @on-visible-change="modalShow" width="850">
    <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
    <Table border :loading="loading" ref="selection" :columns="columns" :data="list" style="margin-top: 10px;"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
  </Drawer>
</template>
<script>
import search from '_c/search' // 查询组件
import { queryExamPaperPage } from '@/api/examModule/memberManagement'
import { queryExamPostList } from '@/api/examModule/jobCategory'
import { queryQuestionTypeList } from '@/api/examModule/questionBankClassify'
export default {
  props: {
    modalData: Object
  },
  components: {
    search
  },
  data () {
    return {
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        insert_time_st: '',
        insert_time_ed: '',
        member_id: '',
        question_type_id: '',
        question_post_id: '',
        pageSize: 10,
        pageIndex: 1
      },
      setSearchData: {
        insert_time: {
          type: 'date_range',
          label: '时间',
          selected: [],
          value: [],
          width: 190,
          isdisable: false
        },
        question_post_id: {
          type: 'select',
          label: '职务',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true
        },
        question_type_id: {
          type: 'select',
          label: '类别',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true
        }
      },
      columns: [
        {
          title: '职务',
          key: 'post_name',
          align: 'center'
        },
        {
          title: '考试类别',
          key: 'type_name',
          align: 'center'
        },
        {
          title: '时间',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, params.row.paper_start_time.substring(0, 10))
          }
        },
        {
          title: '成绩',
          key: 'paper_score',
          align: 'center'
        }
      ]
    }
  },
  methods: {
    // 模态框展示
    modalShow (val) {
      if (val) {
        this.listQuery.member_id = this.modalData.data.member_id
        this.getDataList()
        this.getList()
      }
    },
    // 获取成绩列表
    getList () {
      this.loading = true
      queryExamPaperPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 时间格式
    selectOnChanged (e) {
      if (e.flag === 'date_range') {
        this.listQuery.insert_time_st = e.key[0]
        this.listQuery.insert_time_ed = e.key[1]
      }
    },
    // 查询
    searchResults (e) {
      this.listQuery.question_post_id = e.question_post_id
      this.listQuery.question_type_id = e.question_type_id
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.insert_time_st = ''
      this.listQuery.insert_time_ed = ''
      this.listQuery.question_type_id = ''
      this.listQuery.question_post_id = ''
      this.listQuery.pageSize = 10
      this.listQuery.pageIndex = 1
      this.listCurrent = 1
      this.setSearchData.question_post_id.selected = ''
      this.setSearchData.question_type_id.selected = ''
      this.setSearchData.insert_time.selected = []
      this.getList()
    },
    // 获取题库类别、职务下拉
    getDataList () {
      queryQuestionTypeList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.question_type_id.selectData.push({
              label: item.type_name,
              value: item.question_type_id
            })
          })
        }
      })
      queryExamPostList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.question_post_id.selectData.push({
              label: item.post_name,
              value: item.question_post_id
            })
          })
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
