import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 模块管理列表 无分页
export function querymoduleList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/module/queryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 模块新增
export function addModuleData (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/module/add',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 模块编辑
export function updateModuleData (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/module/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询部门权限
export function queryAllModuleList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/post/module/queryAllList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 配置部门权限
export function configPostModule (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/post/module/config',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  querymoduleList,
  addModuleData,
  updateModuleData,
  queryAllModuleList,
  configPostModule
}
