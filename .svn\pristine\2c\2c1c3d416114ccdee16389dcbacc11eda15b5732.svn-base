<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style lang="less">
.size{
  width: 100%;
  height: 100%;
}
html,body{
  .size;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
#app {
  .size;
}
textarea.ivu-input {
  resize: none;
}
.no-list {
  display: flex !important;
  margin-top: 10px;
  height: 100%;
  min-height: 180px;
  justify-content: center;
  justify-items: center;
  align-items: center;
  background:#fff;
  border-radius:4px;
  border:1px solid #D9D9D9;
  color: #ccc;
  font-size: 24px;
  text-align: center;
  img {
    width: 100px;
  }
}
.bold-font {
  font-size: 14px;
  position: relative;
  color: #333;
  padding-left: 10px;
  margin-bottom: 20px;
  border-left: 5px solid #195BDD;
  line-height: 18px;
}
.bold-font::before {
  content: '';
  position: absolute;
  left: 0;
  border-left: 5px solid #195BDD;
}
.title-font {
  display: inline-block;
  font-size: 20x;
  color: #195BDD;
}
.ivu-btn-primary {
  background-color: #007DFF !important;
  border-color: #007DFF !important;
}
.ivu-page-custom-text {
    border-color: transparent;
    border: 1px solid #C4C6CF !important;
    padding: 0 15px !important;
}
.ivu-modal .ivu-card-head {
    border-bottom: 1px solid #e8eaec;
    padding: 14px 16px 0;
    line-height: 1;
}
.ivu-layout.ivu-layout-has-sider > .ivu-layout {
  display: block;
}
.ivu-drawer-body {
  height: ~"calc(100% - 53px)" !important;
  position: static !important;
}
.ivu-drawer-wrap {
  z-index: 1000 !important;
}
.table-bordered td span {
  white-space: pre-wrap;
}
.drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
</style>
