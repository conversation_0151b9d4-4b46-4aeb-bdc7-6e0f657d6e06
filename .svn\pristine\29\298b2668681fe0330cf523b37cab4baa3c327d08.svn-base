<template>
  <Card>
    <Tabs @on-click="tabClick" v-model="tabId">
      <TabPane label="公司物料" name="tab1">
        <div v-if="tabId === 'tab1'">
          <Row style="margin-bottom: 10px">
            <Col span="14">
              <Button type="primary" @click="handleEdit">新增</Button>
              <span class="but_on">
                <Button type="text" @click="submitSort('list')">提交</Button>
                <Dropdown trigger="click">
                  <Icon type="md-arrow-dropdown"></Icon>
                  <Dropdown-menu slot="list">
                    <Dropdown-item>
                      <div @click="recallSort('list')">撤销</div>
                    </Dropdown-item>
                  </Dropdown-menu>
                </Dropdown>
              </span>
              <span class="but_on">
                <Button type="text" @click="auditSort('list')">审核</Button>
                <Dropdown trigger="click">
                  <Icon type="md-arrow-dropdown"></Icon>
                  <Dropdown-menu slot="list">
                    <Dropdown-item>
                      <div @click="unAuditSort('list')">反审核</div>
                    </Dropdown-item>
                  </Dropdown-menu>
                </Dropdown>
              </span>
              <label style="margin: 0 5px 0 15px">数据状态</label>
              <Select v-model="status" multiple style="width:260px" @on-change="statusChange(status, 'list')">
                <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </Col>
            <Col span="10" style="text-align: right">
              <span style="line-height: 33px;">共{{total}}条结果</span>
              <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
            </Col>
          </Row>
          <Row>
            <Col span="4" style="border: 1px solid #ccc;">
              <h3 class="col-text">物料分类</h3>
              <Tree :data="dataTree" :render="renderContent" class="tree"></Tree>
            </Col>
            <Col span="20" style="padding-left: 10px;">
              <Table
                border
                ref="selection"
                :loading="loading"
                :columns="materiaColumns"
                :data="materialList"
                @on-row-click="tableClick"
                @on-select-all="tableSelectAll"
                @on-select-all-cancel="tableSelectCancel"
                @on-select="tableSelectAll"
                @on-select-cancel="tableSelectCancel"></Table>
              <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
              :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
            </Col>
          </Row>
        </div>
      </TabPane>
      <TabPane label="物料分类" name="tab2">
        <div v-if="tabId === 'tab2'">
          <div style="margin-bottom: 10px">
            <Button type="primary" @click="handleSort">新增</Button>
            <span class="but_on">
              <Button type="text" @click="submitSort('Classi')">提交</Button>
              <Dropdown trigger="click">
                <Icon type="md-arrow-dropdown"></Icon>
                <Dropdown-menu slot="list">
                  <Dropdown-item>
                    <div @click="recallSort('Classi')">撤销</div>
                  </Dropdown-item>
                </Dropdown-menu>
              </Dropdown>
            </span>
            <span class="but_on">
              <Button type="text" @click="auditSort('Classi')">审核</Button>
              <Dropdown trigger="click">
                <Icon type="md-arrow-dropdown"></Icon>
                <Dropdown-menu slot="list">
                  <Dropdown-item>
                    <div @click="unAuditSort('Classi')">反审核</div>
                  </Dropdown-item>
                </Dropdown-menu>
              </Dropdown>
            </span>
            <label style="margin: 0 5px 0 15px">数据状态</label>
            <Select v-model="statusClassi" multiple style="width:260px" @on-change="statusChange(statusClassi, 'Classi')">
              <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
          <Table
            border
            ref="selection"
            @on-select-all="tableSelectAll"
            @on-select-all-cancel="tableSelectCancel"
            @on-select="tableSelectAll"
            @on-select-cancel="tableSelectCancel"
            :loading="loading1"
            :columns="sortColumns"
            :data="sortList"
            maxHeight="500"
            :draggable="true"
            @on-drag-drop="onDragDrop"></Table>
        </div>
      </TabPane>
    </Tabs>
    <!-- 公司物料新增编辑弹窗内容 -->
    <Modal v-model="materialModal"
      :title="titleModal"
      @on-ok="modalType === 'add' ? handleAddSubmit('formInline') : handleEditSubmit('formInline')"
      @on-cancel="handleCancel"
      :width="modalType !== 'file' ? '70%' : '40%'"
      :mask-closable="false"
      id="form_items">
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="100">
        <div v-if="modalType !== 'file'" class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item prop="name" label="物料名称">
                <Input type="text" v-model="formInline.name" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="oldnumber" label="物料编码">
                <Input type="text" v-model="formInline.oldnumber" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="物料状态">
                <Radio-group v-model="formInline.enable" type="button">
                  <Radio label="1" :disabled="formInline.status !== 'A'">启用</Radio>
                  <Radio label="2" :disabled="formInline.status !== 'A'">停用</Radio>
                </Radio-group>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="英文名称">
                <Input type="text" v-model="formInline.xtgf_enname" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="规格">
                <Input type="text" v-model="formInline.modelnum" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="group" label="所属分类">
                <Select v-model="formInline.group" placeholder="请选择" :disabled="formInline.status !== 'A'">
                  <Option v-for="(item, idx) in sortSeleList" :key="idx" :value="item.number">{{ item.name }}</Option>
                </Select>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="单位">
                <Dropdown trigger="custom" :visible="visible">
                  <div :class="visible ? 'drop_down_sel sel_focu' : 'drop_down_sel'" :style="{width: `${formItemWidth}px`}">
                    <a href="javascript:void(0)" @click="formInline.status === 'A' ? handleOpen() : ''" v-html="unit_name === '' ? '请选择' : unit_name"></a>
                    <Icon type="md-arrow-dropdown" color='#333'></Icon>
                  </div>
                  <Dropdown-menu slot="list" :style="{width: `${formItemWidth}px`}">
                    <Dropdown-item v-for="(item, idx) in unitList" :name="item.name" style="padding: 7px 10px">
                      <span v-if="item.isEdit" @click="seleClick(item)" style="width: 80%; display: inline-block;">{{ item.name }}</span>
                      <span v-else><Input type="text" v-model="item.name" style="width: 80%;"></Input></span>
                      <span class="positon-span">
                        <Icon @click="editBtn(item)" type="ios-brush-outline" v-if="item.isEdit"></Icon>
                        <Icon @click="saveBtn(item)" type="md-checkmark" v-else></Icon>
                        <Icon @click="removeBtn(idx)" type="ios-trash-outline"></Icon>
                      </span>
                    </Dropdown-item>
                  </Dropdown-menu>
                </Dropdown>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="制造厂商">
                <Input type="text" v-model="formInline.xtgf_maker" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="供应商">
                <Input type="text" v-model="formInline.xtgf_vendor" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="推荐品牌">
                <Input type="text" v-model="formInline.brand" placeholder="请输入" disabled></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="说明">
                <Input type="text" v-model="formInline.description" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="最高库存">
                <Input-number v-model="formInline.max" placeholder="请输入" style="width:100%" disabled></Input-number>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="安全库存">
                <Input-number v-model="formInline.min" placeholder="请输入" style="width:100%" disabled></Input-number>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="备注">
                <Input type="text" v-model="formInline.xtgf_remark" placeholder="请输入" :readonly="formInline.status !== 'A'"></Input>
              </Form-item>
            </Col>
          </Row>
        </div>
        <div class="tr-name" v-if="modalType !== 'file'">附件</div>
        <fileUpload ref="fileUploadComponent" :fileDataList="fileDataList" type="modalType" @getFileId="getFileId"></fileUpload>
      </Form>
    </Modal>
  </Card>
</template>
<script>
import search from '_c/search'
import fileUpload from '../../performance/performanceTemp/fileUpload'
import API from '@/api/erpSys/common'
import { materialQuery, deleteCompanyMaterial, materialLook } from '@/api/erpSys/materialManager'

export default {
  components: {
    search,
    fileUpload
  },
  data () {
    return {
      treeIdx: null, // 存储当前选中tree的idx
      modalType: '',
      tabId: 'tab1',
      status: ['A', 'B', 'C'],
      statusClassi: ['A', 'B', 'C'],
      statusList: [{
        value: 'A',
        label: '暂存'
      }, {
        value: 'B',
        label: '已提交'
      }, {
        value: 'C',
        label: '已审核'
      }], // 数据状态
      // 公司物料
      visible: false,
      unit_name: '',
      formItemWidth: null,
      sortSeleList: [], // 分类下拉选项
      unitList: [],
      fileDataList: [],
      wps_ids: '',
      materialModal: false,
      titleModal: '',
      modalType: '',
      dataTree: [],
      curDataTree: '',
      materiaColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          render: (h, params) => {
            return h('div', {
              on: {
                click: (e) => {
                  e.stopPropagation()
                }
              }
            }
            )
          }
        },
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '物料编码',
          key: 'oldnumber',
          sortable: true,
          maxWidth: 180,
          align: 'center'
        },
        {
          title: '物料名称',
          key: 'name',
          sortable: true,
          align: 'center'
        },
        {
          title: '规格',
          key: 'modelnum',
          align: 'center'
        },
        {
          title: '单位',
          key: 'baseunit_name',
          width: 85,
          align: 'center'
        },
        {
          title: '说明',
          key: 'description',
          align: 'center'
        },
        {
          title: '数据状态',
          key: 'status',
          align: 'center',
          width: 100,
          render: (h, params) => {
            let curVal = ''
            switch (params.row.status) {
                case 'A':
                  curVal = '暂存'
                  break;
                case 'B':
                  curVal = '已提交'
                  break;
                case 'C':
                  curVal = '已审核'
                  break;
                default:
                  break;
              }
            return h('div', {}, curVal)
          }
        },
        {
          title: '附件',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-link',
                size: 'small',
                type: 'text'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.modalType = 'file'
                  this.titleModal = '附件信息'
                  this.materialModal = true
                  this.handleUpload(params.row.id)
                }
              }
            },
            )
          }
        },
        {
          title: '操作',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-trash',
                size: 'small',
                disabled: true // params.row.status !== 'A'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.handleDelete(params.row)                  
                }
              }
            })
          }
        }
      ],
      materialList: [],
      loading: false,
      total: 0,
      listQuery: {
        number: '',
        status: ['A', 'B', 'C'],
        pageSize: 10,
        pageNo: 1
      },
      listCurrent: 1,
      setSearchData: {
        name: {
          type: 'text',
          width: 180,
          value: '',
          isdisable: false,
          placeholder: '请输入物料名称、物料编码、规格'
        }
      },
      formInline: {},
      ruleInline: {
        name: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        group: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        oldnumber: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      // 物料分类
      selectedIds: [], // 已勾选数据
      selectionData: [],
      currentId: '', // 当前编辑的id
      currentVal: '', // 存储当前编辑的值
      classiToal: null, // 总条数
      classiQuery: {
        number: '03',
        createorg_number: 'xt',
        level: '2',
        status: ['A', 'B', 'C'],
        pageSize: 10000,
        pageNo: 1,
        id: '',
        xtgf_enname: '',
        parent_number: '03',
        creator_number: '00028', // 暂时固定为00028
        xtgf_cgcodetype: ''
      },
      loading1: false,
      sortList: [],
      typeSelList: [{
        id: '13',
        name: '通用物料'
      }, {
        id: '14',
        name: '药品'
      }], // 物料类型
      sortColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '分类编码',
          key: 'number',
          width: 120,
          align: 'center'
        },
        {
          title: '分类名称',
          key: 'name',
          align: 'center',
          render: (h, params) => {
            const inp = h('input', {
              style: {
                height: '32px',
                padding: '5px',
                border: '1px solid #ccc'
              },
              domProps: {
                value: params.row.name
              },
              on: {
                input: (event) => {
                  this.currentVal = event.target.value
                  params.row.name = event.target.value
                }
              }
            })
            return this.currentId === params.row.id ? inp : h('span', params.row.name)
          }
        },
        {
          title: '英文名称',
          key: 'xtgf_enname',
          align: 'center',
          render: (h, params) => {
            const inp = h('input', {
              style: {
                height: '32px',
                padding: '5px 0',
                border: '1px solid #ccc'
              },
              domProps: {
                value: params.row.xtgf_enname
              },
              on: {
                input: (event) => {
                  this.currentVal = event.target.value
                  params.row.xtgf_enname = event.target.value
                }
              }
            })
            return this.currentId === params.row.id ? inp : h('span', params.row.xtgf_enname)
          }
        },
        {
          title: '物料类型',
          key: 'xtgf_cgcodetype',
          width: 180,
          align: 'center',
          render: (h, params) => {
            const inp = h('Select', {
              props: {
                'label-in-value': true,
                value: params.row.xtgf_cgcodetype
              },
              domProps: {
                value: params.row.xtgf_cgcodetype
              },
              on: {
                'on-change': (val) => {
                  this.currentVal = val.label
                  params.row.xtgf_cgcodetype = val.value
                }
              }
            },
            this.typeSelList.map(item => {
              return h('Option', {
                props: {
                  value: item.id,
                  label: item.name
                }
              })
            }))
            let xtgf_type = ''
            if (params.row.xtgf_cgcodetype === '13') {
              xtgf_type = '通用物料'
            } else if (params.row.xtgf_cgcodetype === '14') {
              xtgf_type = '药品'
            }
            return this.currentId === params.row.id ? inp : h('span', xtgf_type)
          }
        },
        {
          title: '数据状态',
          key: 'status',
          align: 'center',
          width: 100,
          render: (h, params) => {
            let curVal = ''
            switch (params.row.status) {
                case 'A':
                  curVal = '暂存'
                  break;
                case 'B':
                  curVal = '已提交'
                  break;
                case 'C':
                  curVal = '已审核'
                  break;
                default:
                  break;
              }
            return h('div', {}, curVal)
          }
        },
        {
          title: '拖拽排序',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'ios-paper',
                size: 'small'
              },
              on: {
                change: () => {
                  this.getSortList()
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: '',
          width: 120,
          align: 'center',
          render: (h, params) => {
            // 编辑
            const btnEdit = h('div', [
              h('i-button', {
                props: {
                  icon: 'md-brush',
                  size: 'small',
                  disabled: params.row.status !== 'A'
                },
                on: {
                  click: () => {
                    this.currentId = params.row.id
                  }
                }
              }),
              h('Button', {
                props: {
                  icon: 'md-trash',
                  size: 'small',
                  disabled: params.row.status !== 'A'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, 'sort')
                  }
                }
              })
            ])
            // 保存
            const btnSave = h('div', [
              h('i-button', {
                props: {
                  icon: 'ios-folder',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleSave(params.row)
                  }
                }
              }),
              h('Button', {
                props: {
                  icon: 'md-trash',
                  size: 'small',
                  disabled: params.row.status !== 'A'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, 'sort')
                  }
                }
              })
            ])
            return this.currentId === params.row.id ? btnSave : btnEdit
          }
        }
      ]
    }
  },
  created () {
    this.getSortList()
    // this.getMaterialList()
  },
  methods: {
    // 获取公司物料列表
    getMaterialList (val) {
      if (val === undefined) {
        this.listQuery.number = '03'
      } else {
        this.listQuery.number = val.number
      }
      this.loading = true
      materialQuery(this.listQuery).then(res => {
        if (res.status) {
          this.loading = false
          this.materialList = res.data.data.rows
          this.total = res.data.data.totalCount
        } else {
          this.loading = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 新增物料
    handleEdit () {
      this.handleCancel()
      this.formInline = {}
      this.titleModal = '新增物料基本信息'
      this.materialModal = true
      this.modalType = 'add'
      this.formInline.enable = '1'
      this.sortSeleList = this.sortList
      this.getUnitList()
      this.formInline.group = this.curDataTree
      this.formItemWidth = (document.getElementById('form_items').clientWidth * 0.7 - 12) / 2 - 138
    },
    // 编辑物料
    tableClick (list) {
      this.materialModal = true
      this.modalType = 'edit'
      this.formInline.enable = '1'
      this.sortSeleList = this.sortList
      this.titleModal = '编辑物料基本信息'
      this.formInline = list
      this.formInline.group = list.group_number
      this.unit_name = list.baseunit_name
      this.getUnitList()
      // this.fileDataList = list.files
      this.formItemWidth = (document.getElementById('form_items').clientWidth * 0.7 - 12) / 2 - 138
    },
    // 新增公司物料保存
    handleAddSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认新增公司物料？</p>',
            loading: true,
            onOk: () => {
              let date = new Date()
              let curyear = date.getFullYear()
              let curmonth = date.getMonth() < 10 ? '0' + (date.getMonth() + 1) : date.getMonth()
              let curday = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
              let curhour = date.getHours() < 10 ? '0' + date.getHours() :　date.getHours()
              let curminutes = date.getMinutes() < 10 ? '0' + date.getMinutes() :　date.getMinutes()
              let curseconds = date.getSeconds() < 10 ? '0' + date.getSeconds() :　date.getSeconds()
              let curDate = curyear + '-' + curmonth + '-' + curday + ' ' + curhour + ':' + curminutes + ':' + curseconds
              let data = {
                materialsList: [{
                  name: this.formInline.name,
                  createorg: {number: '100.01'},
                  baseunit: {number: this.formInline.baseunit},
                  creator: {number: '00027'},
                  modifier: {number: '00027'},
                  approverid: {number: '00027'},
                  createtime: curDate,
                  modifytime: curDate,
                  description: this.formInline.description,
                  modelnum: this.formInline.modelnum,
                  serviceattribute: {number: '1001'},
                  enablepur: true,
                  enablesale: true,
                  oldnumber: this.formInline.oldnumber,
                  enableinv: true,
                  status: 'A',
                  materialtype: 1,
                  ctrlstrategy: 2,
                  xtgf_vendor: this.formInline.xtgf_vendor,
                  xtgf_enname: this.formInline.xtgf_enname,
                  xtgf_maker: this.formInline.xtgf_maker,
                  xtgf_remark: this.formInline.xtgf_remark,
                  group: {number: this.formInline.group},
                  entry_groupstandard: [{
                    groupid: {number: this.formInline.group},
                    standardid: {number: 'JBFLBZ'}
                  }]
                }]
              }
              let dataParam = {
                data: JSON.stringify(data),
                url: '/ierp/kapi/v2/basedata/generatemtbizinfo'
              }
              API.transferStation(dataParam).then(res => {
                if (res.data.Result.status) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.handleCancel()
                  if (this.setSearchData.name.value !== '') {
                    this.searchResults(this.setSearchData.name.value)
                  } else {
                    this.getMaterialList()
                  }
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 编辑公司物料保存
    handleEditSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认修改物料信息？</p>',
            loading: true,
            onOk: () => {
              let data = {
                data: {
                  id: this.formInline.id,
                  name: this.formInline.name,
                  description: this.formInline.description,
                  groupid_number: this.formInline.group, // 分类编码
                  createorg_number: '100.01',
                  baseunit_number: this.formInline.baseunit_number,
                  creator_number: this.formInline.creator_number, // 创建人.工号
                  materialtype: 1,
                  modelnum: this.formInline.modelnum,
                  oldnumber: this.formInline.oldnumber,
                  xtgf_vendor: this.formInline.xtgf_vendor,
                  xtgf_enname: this.formInline.xtgf_enname,
                  xtgf_maker: this.formInline.xtgf_maker,
                  xtgf_remark: this.formInline.xtgf_remark
                }
              }
              let dataParam = {
                data: JSON.stringify(data),
                url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodAddEdit'
              }
              API.transferStation(dataParam).then(res => {
                if (res.data.Result.status) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.handleCancel()
                  if (this.setSearchData.name.value !== '') {
                    this.searchResults(this.setSearchData.name.value)
                  } else {
                    this.getMaterialList()
                  }
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 关闭物料弹窗
    handleCancel () {
      this.modalType = ''
      this.materialModal = false
      this.$refs['formInline'].resetFields()
      this.formInline.unit = ''
      this.formInline.sort = ''
      this.formInline.enable = '1'
      this.visible = false
      this.unit_name = ''
      this.unitList = []
    },
    // 删除列表
    handleDelete (row, type) {
      if (type && row.id === '') {
        this.sortList.splice(row, 1)
      } else {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>删除后无法恢复，是否确认删除？</p>',
          loading: true,
          onOk: () => {
            if (type) { // 物料分类列表
              API.delMaterialClassi({ id: row.id }).then(res => {
                if (res.data.status) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success('删除成功！')
                  this.getMaterialList()
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error('删除失败！')
                }
              })
            } else { // 公司物料
              deleteCompanyMaterial({ id: row.id }).then(res => {
                if (res.data.status) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success('删除成功！')
                  this.getMaterialList()
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error('删除失败！')
                }
              })
            }
          }
        })
      }
    },
    // 附件上传 
    handleUpload (d) {},
    getFileId () {
      this.wps_ids = this.fileDataList.map(item => {
        return item.id
      }).join()
    },
    // 查询公司物料列表
    searchResults (e) {
      this.listCurrent = 1
      if (e.name === '') {
        this.getMaterialList()
      } else {
        let data = {
          name: e.name ? e.name : e,
          number: this.listQuery.number,
          status: this.listQuery.status,
          pageSize: 10,
          pageNo: 1
        }
        this.loading = true
        materialLook(data).then(res => {
          if (res.status) {
            this.loading = false
            this.materialList = res.data.data.rows
            this.total = res.data.data.totalCount
          } else {
            this.loading = false
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 重置公司物料查询结果
    resetResults () {
      this.listCurrent = 1
      this.listQuery.pageSize = 10
      this.listQuery.pageNo = 1
      this.setSearchData.name.value = ''
      this.curDataTree = ''
      // this.dataTree = []
      this.dataTree[this.treeIdx].selected = undefined
      this.getMaterialList()
    },
    // 公司物料-页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getMaterialList()
    },
    // 公司物料-分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageNo = val
      this.getMaterialList()
    },
    // ...物料分类...//
    // 获取分类列表
    getSortList () {
      this.loading1 = true
      API.getEquipList(this.classiQuery).then(res => {
        this.loading1 = false
        if (res.status === 200) {
          this.sortList = res.data.data.rows
          this.classiToal = res.data.data.totalCount
          if (this.tabId === 'tab1') {
            res.data.data.rows.map((item, idx) => {
              this.dataTree.push({
                title: item.name,
                id: item.id,
                number: item.number,
                render: (h, { root, node, data }) => {
                  return h('span', {
                    style: {
                      display: 'inline-block',
                      width: '100%'
                    },
                    on: {
                      click: () => {
                        this.treeIdx = idx
                        if (!data.selected) {
                          this.getMaterialList(data)
                          this.curDataTree = data.number
                        } else {
                          this.curDataTree = ''
                          this.getMaterialList()
                        }
                      }
                    }
                  }, [
                    h('span', [
                      h('Icon', {
                        props: {
                          type: 'ios-home-outline'
                        },
                        style: {
                          marginRight: '8px'
                        }
                      }),
                      h('span', data.title)
                    ])
                  ])
                }
              })
            })
          } else {
            this.dataTree = []
          }
        }
      })
    },
    // 新增分类
    handleSort () {
      if (this.sortList[0].id === '') {
        this.$Message.error('请先保存后，才能操作下一条！')
      } else {
        let total = this.classiToal < 10 ? '0' + this.classiToal + 1 : this.classiToal + 1
        this.sortList.unshift({
          id: '',
          name: '',
          xtgf_enname: '',
          number: this.sortList[0].number.substring(0, 2) + total,
          parent_number: '03',
          creator_number: '00028', // 暂时固定为00028
          createorg_number: 'xt',
          xtgf_cgcodetype: '',
          status: 'A'
        })
      }
    },
    // 保存分类列表编辑
    handleSave (row) {
      if (row.xtgf_enname === '' || row.name === '' || row.xtgf_cgcodetype === '') {
        this.$Message.error('带*号的为必填项')
      } else {
        this.sortList = this.sortList.map(v => {
          return v.id === row.id ? { ...v, ...this.currentVal } : v
        })
        this.currentId = ''
        this.currentVal = ''
        let data = {}
        if (row.id === '') {
          delete row.id
          data = row
        } else {
          data = {
            id: row.id,
            name: row.name,
            xtgf_enname: row.xtgf_enname,
            number: row.number,
            parent_number: '03',
            creator_number: '00028', // 暂时固定为00028
            createorg_number: 'xt',
            xtgf_cgcodetype: row.xtgf_cgcodetype,
            status: row.status
          }
        }
        this.$Modal.confirm({
          title: '提示',
          content: row.id ? '<p>确认保存编辑分类？</p>' : '<p>确认新增分类？</p>',
          loading: true,
          onOk: () => {
            API.addEditMaterial(data).then(res => {
              if (res.data.status) {
                this.loading = false
                this.$Modal.remove()
                this.$Message.success('操作成功！')
                this.getSortList()
              } else {
                this.loading = false
                this.$Modal.remove()
                this.$Message.error('操作失败！')
              }
            })
          }
        })
      }
    },
    // 拖拽排序
    onDragDrop(first, end) {
      //转成int型，方便后续使用
      first = parseInt(first)
      end = parseInt(end)
      let tmp = this.sortList[first]
      if(first < end) {
        for(var i=first+1; i<=end; i++) {
          this.sortList.splice(i-1, 1, this.sortList[i])
        }
        this.sortList.splice(end, 1, tmp)
      }
      if(first > end) {
        for(var i=first; i>end; i--) {
          this.sortList.splice(i, 1, this.sortList[i-1])
        }
        this.sortList.splice(end, 1, tmp)
      }
      // let idx = this.sortList.findIndex(e => {return e.id === ''})
      // if (idx > -1) {
      //   this.sortList.splice(this.sortList[idx], 1)
      // }
      if (this.sortList[0].id === '') {
        this.sortList.splice(this.sortList[0], 1)
      }
      this.getSortList()
    },
    // 获取计量单位
    getUnitList () {
      let data = {
        enable: '1',
        status: 'C',
        pageSize: '10000',
        pageNo: '1'
      }
      API.getUnitList(data).then(res => {
        if (res.status === 200) {
          res.data.data.rows.map(item => {
            this.unitList.push({
              isEdit: true,
              name: item.name,
              number: item.number
            })
          })
        }
      })
    },
    // 编辑单位下拉项
    editBtn (item) {
      item.isEdit = false
    },
    // 保存单位下拉项
    saveBtn (item) {
      item.isEdit = true
    },
    // 删除单位下拉项
    removeBtn (index) {
      this.unitList.splice(index, 1)
    },
    // 点击单位下拉项触发
    seleClick (item) {
      this.visible = false
      this.unit_name = item.name
      this.formInline.baseunit = item.number
      // this.formInline.baseunit_name = item.name
    },
    // 打开单位下拉
    handleOpen () {
      this.visible = true
    },    
    // 左侧分类树
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
          click: () => {
            this.getMaterialList(data.id)
          }
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', data.title)
        ])
      ])
    },
    // tab切换
    tabClick (name) {
      this.tabId = name
      this.clearData()
      if (name === 'tab1') {
        this.getSortList()
      }
    },
    // 清除数据
    clearData () {
      this.selectedIds = []
      this.selectionData = []
    },
    // 提交
    submitSort (type) {
      let idx = this.selectionData.findIndex(item => item.status !== 'A')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: type === 'list' ? '/ierp/kapi/v2/xtgf/basedata/bd_material/goodSubmit' : '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsSubmit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              this.clearData()
              this.$Message.success('提交成功。')
              if (type === 'list') {
                this.getMaterialList()
              } else {
                this.getSortList()
              }
            }
          })
        }
      } else {
        this.$Message.error('只有暂存的数据才允许提交。')
      }
    },
    // 撤销提交
    recallSort (type) {
      let idx = this.selectionData.findIndex(item => item.status !== 'B')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: type === 'list' ? '/ierp/kapi/v2/xtgf/basedata/bd_material/goodUnsubmit' : '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsUnsubmit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              this.clearData()
              this.$Message.success('撤销成功。')
              if (type === 'list') {
                this.getMaterialList()
              } else {
                this.getSortList()
              }
            }
          })
        }
      } else {
        this.$Message.error('只有已提交的数据才能撤销。')
      }
    },
    // 审核
    auditSort (type) {
      let idx = this.selectionData.findIndex(item => item.status !== 'B')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: type === 'list' ? '/ierp/kapi/v2/xtgf/basedata/bd_material/goodAudit' : '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsAudit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              this.clearData()
              this.$Message.success('审核成功。')
              if (type === 'list') {
                this.getMaterialList()
              } else {
                this.getSortList()
              }
            }
          })
        }
      } else {
        this.$Message.error('只有已提交的数据才能审核。')
      }
    },
    // 反审核
    unAuditSort (type) {
      let idx = this.selectionData.findIndex(item => item.status !== 'C')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: type === 'list' ? '/ierp/kapi/v2/xtgf/basedata/bd_material/goodUnAudit' : '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsUnaudit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              this.clearData()
              this.$Message.success('反审核成功。')
              if (type === 'list') {
                this.getMaterialList()
              } else {
                this.getSortList()
              }
            }
          })
        }
      } else {
        this.$Message.error('只有已审核的数据才能反审核。')
      }
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
      if (selection.length === 0) {
        this.selectedIds = []
      } else {
        this.selectedIds.forEach((e, idx) => {
          if (e === row.id) {
            this.selectedIds.splice(idx, 1)
          }
        })
      }
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.sortList.length) this.selectAll = true
      this.selectionData = selection
      if (row === undefined) {
        selection.map(e => {
          this.selectedIds.push(e.id)
        })
      } else {
        this.selectedIds.push(row.id)
      }
    },
    // 数据状态选择
    statusChange (val, type) {
      if (type === 'list') {
        this.listQuery.status = val.length === 0 ? ['A', 'B', 'C'] : val
        if (this.setSearchData.name.value !== '') {
          this.searchResults(this.setSearchData.name.value)
        } else {
          this.getMaterialList()
        }
      } else {
        this.classiQuery.status = val.length === 0 ? ['A', 'B', 'C'] : val
        this.getSortList()
      }
    }
  }
}
</script>
<style lang="less" scoped>
.drop_down_sel {
  padding: 0 10px;
  border-radius: 5px;
  border: 1px solid #dcdee2;
  a {
    color: #666;
    width: 95%;
    display: inline-block;
  }
}
.drop_down_sel.sel_focux {
  border: 1px solid #2D8cF0;
}
.positon-span {
  text-align: right;
  margin-left: 5px;
  i {
    padding: 0 5px;
    font-size: 16px;
  }
}
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.search-vie {
  float: right;
  margin-top: -5px;
  margin-left: 10px;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.but_on {
  margin: 0 5px;
  position: relative;
  display: inline-block;
  border-radius: 5px;
  z-index: 999;
  line-height: 32px;
  color: #fff;
  background-color: #2d8cf0;
  button.ivu-btn-text {
    color: #fff;
    padding: 0 0 0 10px;
    &:hover {
      background-color:transparent;
    }
  }
  .ivu-dropdown {
    padding: 0 5px;
  }
}
</style>
<style>
#form_items .ivu-form-item-error-tip {
  z-index: 10;
  margin-left: 10px;
}
.tree.ivu-tree ul li {
  margin: 5px 0;
}
.tree .ivu-tree-title span {
  font-size: 16px;
  color: #666;
}
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
}
</style>
