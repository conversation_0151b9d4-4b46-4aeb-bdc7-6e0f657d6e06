<template>
    <router-view />
</template>
<script>
export default {
    name: 'noMenuMain',
    components: {
    },
    data() {
        return {
           
        }
    },
   
    mounted() {
        if (this.$store.state.user.menuList.length > 0) {
            return
        }
        if (this.$route.name !== 'home' && !this.UNLOGIN_PAGE_LIST.includes(this.$route.name)) {
            console.log(1111);
            this.$router.push({
                name: 'error_401'
            })
        }
    }
}
</script>