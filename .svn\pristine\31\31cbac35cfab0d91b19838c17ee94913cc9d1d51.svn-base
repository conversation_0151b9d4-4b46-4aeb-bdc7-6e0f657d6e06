import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 部门列表查找 分页
export function queryDepartManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/queryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 部门列表查找 无分页
export function queryDepartManageList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/queryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加部门信息
export function addDepartManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/add',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改部门信息
export function updateDepartManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除部门信息
export function delDepartManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/delete',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 职务列表查找 分页
export function queryPostManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/post/queryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 职务列表查找 不分页
export function queryPostManageList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/post/queryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加职务信息
export function addPostManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/post/add',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改职务信息
export function updatePostManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/post/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除职务信息
export function delPostManagePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/dept/post/delete',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 绩效统计
export function queryPerfStatInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/stat/queryPerfStatInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryDepartManagePage,
  queryDepartManageList,
  addDepartManagePage,
  updateDepartManagePage,
  delDepartManagePage,
  queryPostManagePage,
  queryPostManageList,
  addPostManagePage,
  updatePostManagePage,
  delPostManagePage,
  queryPerfStatInfo
}
