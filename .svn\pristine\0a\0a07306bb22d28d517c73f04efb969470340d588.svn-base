import axios from 'axios'
import { getToken, getShipCompanyId } from '@/libs/util'
import router from '../router'
import Cookies from 'js-cookie'

class HttpRequest {
  constructor (baseUrl = baseURL) {
    this.baseUrl = baseUrl
    this.queue = {}
  }
  getInsideConfig () {
    const config = {
      baseURL: this.baseUrl,
      headers: {
        // 'Content-Type': 'application/json',
        // 'Access-Control-Allow-Origin': 'http://erp.xtshipping.net'
      }
    }
    return config
  }
  destroy (url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
    }
  }
  interceptors (instance, url) {
    // 请求拦截
    instance.interceptors.request.use(config => {
      // 添加全局的loading...
      if (!Object.keys(this.queue).length) {
        // Spin.show() // 不建议开启，因为界面不友好
      }
      this.queue[url] = true
      // 统一拦截添加token
      config.params = {}
      config.params.token = getToken()
      // if (getShipCompanyId() && getShipCompanyId() !== '') {
      //   config.params.ship_company_id = getShipCompanyId()
      // }
      return config
    }, error => {
      return Promise.reject(error)
    })
    // 响应拦截
    instance.interceptors.response.use(res => {
      this.destroy(url)
      const { data, status } = res
      // 接口token过期处理
      if (res.data.Code === -202) {
        Cookies.set('token', '')
        router.push({ name: 'login', params: { failure: true } })
      }
      return { data, status }
    }, error => {
      this.destroy(url)
      return Promise.reject(error)
    })
  }
  request (options) {
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}
export default HttpRequest
