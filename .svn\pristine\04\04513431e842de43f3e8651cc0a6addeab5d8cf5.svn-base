<template>
  <Card>
    <search @searchResults='searchResults' :setSearch='searchData'  @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
    <Table :data="dataList" :columns="columns"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
    :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
  </Card>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/performance'
import deptAPI from '@/api/jurisdictionManage/departManagement'
import { queryUnifiedAccountList } from '@/api/jurisdictionManage/userManagement'

export default ({
  components: {
    search
  },
  data () {
    return {
      total: 0,
      dataList: [],
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        unified_account_id: '',
        dept_id: '',
        belong_month: '',
        is_finish: '' // 表单状态（0拟稿，1审核中，2完结）
      },
      columns: [
        {
          title: '姓名',
          key: 'user_name',
          align: 'center'
        },
        {
          title: '部门',
          key: 'dept_name',
          align: 'center'
        },
        {
          title: '绩效月份',
          key: 'belong_month',
          align: 'center'
        },
        {
          title: '自评',
          key: 'self_evaluate_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '最终得分',
          key: 'final_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '状态',
          key: 'is_finish',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            let statusStr = ''
            if (params.row.is_finish === '0') statusStr = '拟稿'
            if (params.row.is_finish === '1') statusStr = '审核中'
            if (params.row.is_finish === '2') statusStr = '已完成'
            if (params.row.is_finish === '-1') statusStr = '未起草'
            return h('div', {}, statusStr)
          }
        },
        {
          title: '操作',
          key: 'date',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }, '查看'),
              h('Button', {
                style: {
                  margin: '0 5px',
                  display: params.row.is_finish === '2' ? 'inline-block' : 'none'
                },
                props: {
                  icon: 'md-color-wand',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleSubmit(params.row)
                  }
                }
              }, '重新审批')
            ])
          }
        }
      ],
      searchData: {
        unified_account_id: {
          type: 'select',
          label: '姓名',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          change: this.userChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        },
        date: {
          type: 'month',
          label: '绩效月份',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        },
        dept_id: {
          type: 'select',
          label: '部门',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          change: this.deptChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        },
        status: {
          type: 'select',
          label: '状态',
          selectData: [{
            value: 0,
            label: '拟稿'
          },
          {
            value: 1,
            label: '审核中'
          },
          {
            value: 2,
            label: '已完成'
          },
          {
            value: -1,
            label: '未起草'
          }],
          selected: '',
          placeholder: '请选择',
          change: this.statusChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        }
      }
    }
  },
  methods: {
    // 档案列表
    getList () {
      API.querySysPerformancePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.dataList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 获取部门列表数据
    getDeptList () {
      deptAPI.queryDepartManageList().then(res => {
        if (res.data.Code === 10000) {
          this.searchData.dept_id.selectData = res.data.Result.map(item => {
            return {
              value: item.dept_id,
              label: item.dept_name
            }
          })
        }
      })
    },
    // 获取成员列表数据
    getAccountList () {
      queryUnifiedAccountList().then(res => {
        if (res.data.Code === 10000) {
          this.searchData.unified_account_id.selectData = res.data.Result.map(item => {
            return {
              value: item.unified_account_id,
              label: item.user_name
            }
          })
        }
      })
    },
    // 查看详情
    handleDetail (item) {
      if (item.form_json && item.form_json.formType) {
        localStorage.setItem('formType', item.form_json.formType)
      }
      this.$router.push({
        name: 'performanceDetail',
        params: {
          id: 'detail&id=' + item.form_id
        }
      })
    },
    handleSubmit (item) { // 重新审批
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认要提交该流程？</p>',
        loading: true,
        onOk: () => {
          API.submitPerfForm({
            form_id: item.form_id,
            draft_unified_account_id: item.unified_account_id,
            current_unified_account_id: -2, // 系统退回 -2
            next_unified_account_id: item.unified_account_id,
            dept_flow_id: item.dept_flow_id,
            belong_month: item.belong_month,
            month_bonus: item.month_bonus ? parseFloat(item.month_bonus) : 0,
            extra_bonus: item.extra_bonus ? parseFloat(item.extra_bonus) : 0,
            extra_bonus_bak: item.extra_bonus_bak ? item.extra_bonus_bak : '',
            execute_code: -2,
            form_json: JSON.stringify(item.form_json)
          }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    // 用户改变
    userChange (e) {
      this.listQuery.unified_account_id = e.selected
    },
    // 部门改变
    deptChange (e) {
      this.listQuery.dept_id = e.selected
    },
    // 状态改变
    statusChange (e) {
      this.listQuery.is_finish = e.selected
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.belong_month = e.key
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    // 查询
    searchResults (e) {
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.belong_month = ''
      this.listQuery.is_finish = ''
      this.listQuery.dept_id = ''
      this.listQuery.unified_account_id = ''
      this.searchData.date.selected = ''
      this.searchData.unified_account_id.selected = ''
      this.searchData.dept_id.selected = ''
      this.searchData.status.selected = ''
      this.$store.state.setState.performanceProfileParam = {}
      this.getList()
    }
  },
  beforeDestroy () {
    this.$store.commit('setPerformanceProfile', this.listQuery)
  },
  created () {
    // 获取存储的入参
    if (Object.keys(this.$store.state.setState.performanceProfileParam).length > 0) {
      let storeQuery = this.$store.state.setState.performanceProfileParam
      this.listQuery = this.$store.state.setState.performanceProfileParam
      this.searchData.unified_account_id.selected = storeQuery.unified_account_id
      this.searchData.date.selected = storeQuery.belong_month
      this.searchData.dept_id.selected = storeQuery.dept_id
      this.searchData.status.selected = storeQuery.is_finish
    }
    this.getDeptList()
    this.getAccountList()
    this.getList()
  }
})
</script>
