<template>
  <div>
    <h3>一、本月工作总结，分值90分。   质量评价：优秀100分、优良95分、符合要求：85分、基本符合80分；待改进：≤75分，完成率：工作进度完成率 </h3>
    <table class="table table-bordered" style="width: calc(100% - 40px)">
      <!-- 表头开始 -->
      <tr>
        <th rowspan="2" style="width: 50px;">序号</th>
        <th rowspan="2" style="width: 130px;">名称</th>
        <th rowspan="2">主要工作事项</th>
        <th rowspan="2">交付成果或完成情况描述</th>
        <th rowspan="2" style="width: 60px;">配分(85分)</th>
        <th colspan="2">完成率(60%)</th>
        <th colspan="2">质量评价(40%)</th>
        <th colspan="2">单项得分</th>
        <th rowspan="2">备注</th>
      </tr>
      <tr>
        <th style="width: 60px;">自评</th>
        <th style="width: 60px;">上级</th>
        <th style="width: 60px;">自评</th>
        <th style="width: 60px;">上级</th>
        <th style="width: 60px;">自评</th>
        <th style="width: 60px;">上级</th>
      </tr>
      <!-- 表头结束 -->

      <!-- 重点工作事项开始 -->
      <tr v-for="(item, idx) in curKeyList" :key="'key' + idx">
        <td class="center">{{ idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="curKeyList.length">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td>{{ item.result }}</td>
        <td class="center">{{ item.score }}</td>
        <td class="center">{{ item.selfRate }}</td>
        <td class="center">{{ item.supRate }}</td>
        <td class="center">{{ item.selfQuality }}</td>
        <td class="center">{{ item.supQuality }}</td>
        <td class="center">{{ item.selfScore }}</td>
        <td class="center">{{ item.supScore }}</td>
        <td class="center">{{ item.remark }}</td>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (curKeyList.length - 1)">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="idx > 0">-</Button>
      </tr>
      <!-- 重点工作事项结束 -->

      <!-- 一般工作事项开始 -->
      <tr v-for="(item, idx) in curCommonList" :key="'common' + idx">
        <td class="center">{{ curKeyList.length + idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="curCommonList.length">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td>{{ item.result }}</td>
        <td class="center">{{ item.score }}</td>
        <td class="center">{{ item.selfRate }}</td>
        <td class="center">{{ item.supRate }}</td>
        <td class="center">{{ item.selfQuality }}</td>
        <td class="center">{{ item.supQuality }}</td>
        <td class="center">{{ item.selfScore }}</td>
        <td class="center">{{ item.supScore }}</td>
        <td class="center">{{ item.remark }}</td>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (curCommonList.length - 1)">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="idx > 0">-</Button>
      </tr>
      <!-- 一般工作事项结束 -->
      <!-- 合计开始 -->
      <tr>
        <td class="center">{{ curKeyList.length + curCommonList.length + 1 }}</td>
        <td></td>
        <td></td>
        <td class="center">合计</td>
        <td class="center">{{ selfTotalScore }}</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <!-- 合计结束 -->
      <tr>
        <td colspan="2" rowspan="2">加减分(5分，附加事项说明)</td>
        <td class="center">加分</td>
        <td colspan="6">加分内容</td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td class="center">减分</td>
        <td colspan="6">减分内容</td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>加减分说明</td>
        <td colspan="7">
          <span v-html="addRemoveTxt"></span>
        </td>
        <td>小计</td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
    </table>
    <h3>二、本月工作小结，分值2分（上月计划调整未说明的，每件减1分）。</h3>
    <table class="table table-bordered" style="width: calc(100% - 40px)">
      <tr>
        <td style="width: 135px;">上月计划调整说明</td>
        <td></td>
      </tr>
      <tr>
        <td style="width: 135px;">待改进事项(1分)</td>
        <td></td>
      </tr>
      <tr>
        <td style="width: 135px;">改进对策与建议(1分)</td>
        <td></td>
      </tr>
    </table>
    <h3>三、下月工作计划，分值8分。</h3>
    <table class="table table-bordered" style="width: calc(100% - 40px)">
      <tr>
        <th style="width: 50px;">序号</th>
        <th style="width: 145px;">名称</th>
        <th>工作事项</th>
        <th>交付成果或达成目标</th>
        <th style="width: 120px;">计划起止时间</th>
        <th>备注</th>
      </tr>
      <tr v-for="(item, idx) in planKeyList" :key="'key' + idx">
        <td class="center">{{ idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="planKeyList.length">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td>{{ item.result }}</td>
        <td class="center">{{ item.time }}</td>
        <td class="center">{{ item.remark }}</td>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (planKeyList.length - 1)">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="idx > 0">-</Button>
      </tr>
      <tr v-for="(item, idx) in planCommonList" :key="'common' + idx">
        <td class="center">{{ curKeyList.length + idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="planCommonList.length">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td>{{ item.result }}</td>
        <td class="center">{{ item.time }}</td>
        <td class="center">{{ item.remark }}</td>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (planCommonList.length - 1)">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="idx > 0">-</Button>
      </tr>
    </table>
    <h3>四、月度考核总体评价</h3>
    <table class="table table-bordered" style="width: calc(100% - 40px)">
      <tr>
        <th style="width: 100px;">类别</th>
        <th>内容</th>
        <th style="width: 60px;">配分</th>
        <th style="width: 70px;">自评得分</th>
        <th style="width: 70px;">上级得分</th>
        <th>备注</th>
      </tr>
      <tr v-for="(item, idx) in examObj.curExamList" :key="'curExam' + idx">
        <td v-if="idx === 0" :rowspan="examObj.curExamList.length" class="center">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td class="center">{{ item.score }}</td>
        <td class="center">{{ item.selfScore }}</td>
        <td class="center">{{ item.supScore }}</td>
        <td>{{ item.remark }}</td>
      </tr>
      <tr v-for="(item, idx) in examObj.planExamList" :key="'planExam' + idx">
        <td v-if="idx === 0" :rowspan="examObj.planExamList.length" class="center">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td class="center">{{ item.score }}</td>
        <td class="center">{{ item.selfScore }}</td>
        <td class="center">{{ item.supScore }}</td>
        <td>{{ item.remark }}</td>
      </tr>
      <tr>
        <td colspan="2" class="center">小计</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td>说明：</td>
        <td colspan="5"></td>
      </tr>
      <tr>
        <td colspan="6" v-html="examObj.explainTxt"></td>
      </tr>
    </table>
  </div>
</template>
<script>

export default ({
  data () {
    return {
      addRemoveTxt: '1、加减分说明内容加减分说明内容加减分说明内容<br> 2、加减分说明内容加减分说明内容加减分说明内容',
      curKeyList: [
        {
          title: '重点工作事项(60分)',
          content: '营运效率统计系统数据核对功能',
          result: '完成测试，上线',
          score: '20',
          selfRate: '100%',
          supRate: '90%',
          selfQuality: '100%',
          supQuality: '95%',
          selfScore: '95',
          supScore: '93',
          remark: '备注'
        },
        {
          title: '重点工作事项(60分)',
          content: '营运效率统计系统数据核对功能',
          result: '完成测试，上线',
          score: '20',
          selfRate: '100%',
          supRate: '90%',
          selfQuality: '100%',
          supQuality: '95%',
          selfScore: '95',
          supScore: '93',
          remark: '备注'
        },
        {
          title: '重点工作事项(60分)',
          content: '营运效率统计系统数据核对功能',
          result: '完成测试，上线',
          score: '20',
          selfRate: '100%',
          supRate: '90%',
          selfQuality: '100%',
          supQuality: '95%',
          selfScore: '95',
          supScore: '93',
          remark: '备注'
        }
      ],
      curCommonList: [
        {
          title: '一般工作事项(25分)',
          content: '航次动态问题反馈',
          result: '及时处理船上反馈问题',
          score: '10',
          selfRate: '100%',
          supRate: '90%',
          selfQuality: '100%',
          supQuality: '95%',
          selfScore: '95',
          supScore: '93',
          remark: '备注'
        },
        {
          title: '一般工作事项(25分)',
          content: '航次动态问题反馈',
          result: '及时处理船上反馈问题',
          score: '10',
          selfRate: '100%',
          supRate: '90%',
          selfQuality: '100%',
          supQuality: '95%',
          selfScore: '95',
          supScore: '93',
          remark: '备注'
        }
      ],
      planKeyList: [
        {
          title: '重点事项工作计划(5分)',
          content: '一体化管理平台基础用户合并功能',
          result: '完成测试，上线',
          time: '3.25-4.13',
          remark: '备注'
        },
        {
          title: '重点事项工作计划(5分)',
          content: '一体化管理平台基础用户合并功能',
          result: '完成测试，上线',
          time: '3.25-4.13',
          remark: '备注'
        }
      ],
      planCommonList: [
        {
          title: '一般事项工作计划(3分)',
          content: '一体化管理平台基础用户合并功能',
          result: '完成测试，上线',
          time: '3.25-4.13',
          remark: '备注'
        },
        {
          title: '一般事项工作计划(3分)',
          content: '一体化管理平台基础用户合并功能',
          result: '完成测试，上线',
          time: '3.25-4.13',
          remark: '备注'
        }
      ],
      examObj: {
        totalScore: '100',
        explain: '',
        explainTxt: '说明：1、本月总结与计划工作表共100分，其中：工作总结90分、工作小结2分、工作计划8分;<br>2、质量评价参考标准：优秀(100分)：有创新、或效率有大幅提升或成果被推广应用或受领导或客户表扬等超出预期的工作质量，优良(95分)：工作/服务质量比以往有一定提升和改进、工作/服务质量好于预期，符合要求(85分)：质量与以往相同，基本能达到要求;基本符合(80分)：质量稍有差距，或略加指导能满足要求，待改进(75分)：质量有较大差距或需经较大调整或3次以上改进才能满足要求。<br>3、单项自评得分=配分*(自评完成率*60% + 质量自评%*40%),单项上级评价得分=配分*(自评完成率*60%+上级质量评分%*40%)<br>4、本月工作小结共2分，原计划调整未说明情况的，每项扣1分;本月提供待改进事项和改进对策与建议，在下月考核中整改落实到位或建议得到实施的加1分;',
        curExamList: [
          {
            title: '本月总结',
            content: '本月工作总结',
            score: '90',
            selfScore: '80',
            supScore: '80',
            remark: ''
          },
          {
            title: '本月总结',
            content: '待改进事项',
            score: '1',
            selfScore: '',
            supScore: '',
            remark: ''
          },
          {
            title: '本月总结',
            content: '改进对策与建议',
            score: '2',
            selfScore: '',
            supScore: '',
            remark: ''
          }
        ],
        planExamList: [
          {
            title: '下月计划',
            content: '下月重点事项计划',
            score: '5',
            selfScore: '',
            supScore: '',
            remark: ''
          },
          {
            title: '下月计划',
            content: '下月一般事项计划',
            score: '3',
            selfScore: '',
            supScore: '',
            remark: ''
          }
        ]
      }
    }
  },
  computed: {
    selfTotalScore () {
      let keyTotal = 0
      let commonTotal = 0
      this.curKeyList.map(item => {
        keyTotal += parseFloat(item.score)
      })
      this.curCommonList.map(item => {
        commonTotal += parseFloat(item.score)
      })
      return (keyTotal + commonTotal)
    }
  },
  methods: {

  }
})
</script>
<style scoped lang="less">
  .add_btn {
    position: absolute;
    right: -55px;
  }
  .remove_btn {
    position: absolute;
    right: -25px;
  }
  .table-bordered {
    border-left:2px solid #e8eaec;
    border-top:2px solid #e8eaec;
  }
  .table-bordered tr {
    position: relative;
  }
  .table-bordered th{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    text-align: center;
    vertical-align: middle;
    padding: 0 10px;
    input {
      color: #999;
    }
  }
  .table-bordered td{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
    padding: 0 10px;
  }
  .table-bordered .center {
    text-align: center;
  }
</style>
