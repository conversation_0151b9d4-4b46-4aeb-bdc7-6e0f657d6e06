<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="300" @on-visible-change="modalShowHide" :mask-closable="false" ok-text="保存" @on-ok=
  "editHandle">
    <Form ref="formValide" :model="userData" :label-width="85" style="margin-bottom: 20px;">
      <FormItem label="成员">
        <Select v-model="userData.unified_account_id" filterable label-in-value @on-change="accountChange">
          <Option v-for="item in userList" :key="item.unified_account_id" :value="item.unified_account_id">{{ item.user_name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="一体化账号">
         <Input type='text' v-model='userData.unified_account' :disabled="true"></Input>
      </FormItem>
      <FormItem label="瀛海账号">
         <Input type='text' v-model='userData.erp_account' :disabled="true"></Input>
      </FormItem>
      <FormItem label="海运管家账号">
         <Input type='text' v-model='userData.hzx_account' :disabled="true"></Input>
      </FormItem>
      <FormItem label="职务">
        <Select v-model="userData.post_id" filterable label-in-value @on-change="obj => obj ? userData.post_name = obj.label : ''">
          <Option v-for="item in postList" :key="item.post_id" :value="item.post_id">{{ item.post_name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="绩效KPI说明">
         <Input type='text' v-model='userData.perf_points_remark'></Input>
      </FormItem>
      <FormItem label="绩效资金额度">
         <Input type='text' v-model='userData.perf_bonus'></Input>
      </FormItem>
      <FormItem label="绩效资金规则">
         <Input type='textarea' :autosize="{minRows: 2, maxRows: 5}" v-model='userData.perf_bonus_remark'></Input>
      </FormItem>
    </Form>
  </Modal>
</template>
<script>
import API from '@/api/jurisdictionManage/departManagement'
import { queryUnifiedAccountList, addPostStaffPage, updatePostStaffPage } from '@/api/jurisdictionManage/userManagement'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      userData: {},
      userList: [], // 成员列表
      postList: [] // 职务列表
    }
  },
  methods: {
    // 编辑保存
    editHandle () {
      if (this.modalData.type === 'add') { // 新增
        addPostStaffPage({
          dept_id: this.userData.dept_id,
          staff_id: this.userData.unified_account_id,
          post_id: this.userData.post_id,
          perf_bonus: this.userData.perf_bonus,
          perf_points_remark: this.userData.perf_points_remark,
          perf_bonus_remark: this.userData.perf_bonus_remark
        }).then(res => {
          this.$emit('userDataBack')
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.modalData.type === 'modify') { // 编辑
        updatePostStaffPage({
          dept_staff_post_id: this.userData.dept_staff_post_id,
          dept_id: this.userData.dept_id,
          staff_id: this.userData.unified_account_id,
          post_id: this.userData.post_id,
          perf_bonus: this.userData.perf_bonus,
          perf_points_remark: this.userData.perf_points_remark,
          perf_bonus_remark: this.userData.perf_bonus_remark
        }).then(res => {
          this.$emit('userDataBack')
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    accountChange (obj) {
      if (obj) {
        this.userData.user_name = obj.label
        let _curList = this.userList.filter(item => item.unified_account_id === obj.value)
        this.userData.unified_account = _curList[0].unified_account
        this.userData.erp_account = _curList[0].erp_account
        this.userData.hzx_account = _curList[0].hzx_account
        this.userData.perf_bonus = _curList[0].perf_bonus
        this.userData.perf_points_remark = _curList[0].perf_points_remark
        this.userData.perf_bonus_remark = _curList[0].perf_bonus_remark
      }
    },
    // 获取成员及职务下拉列表
    getBasicList () {
      queryUnifiedAccountList().then(res => {
        if (res.data.Code === 10000) {
          this.userList = res.data.Result
        }
      })
      API.queryPostManageList({
        dept_id: this.userData.dept_id
      }).then(res => {
        if (res.data.Code === 10000) {
          this.postList = res.data.Result
        }
      })
    },
    // 弹窗显示
    modalShowHide (val) {
      if (val) {
        this.userData = { ...{}, ...this.modalData.data }
        this.getBasicList()
      }
    }
  }
})
</script>
