<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <ChartLine style="height: 300px; margin-top: 27px;" :unit="curUnit" :value="lineData" :color="lineColor" rotate="45" :text="curInventoryname + '采购总量'" />
      <h3 class="text_con">
        {{ curInventoryname }}采购数据
        <Button type="text" @click="exportData">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
      </h3>
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list" class="table_wrap"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import { ChartLine } from '_c/charts'
import API from '@/api/basicData'
import { queryMaterialPage, exportMaterial } from '@/api/materialPurchaseSystem/materialSinglePrice'
import { queryMaterialMonthPage } from '@/api/materialPurchaseSystem/materialNum'

export default {
  components: {
    search,
    ChartLine
  },
  data () {
    return {
      curUnit: '吨', // 当前单位
      curInventoryname: '',
      queryParam: {
        date_month_st: '',
        date_month_et: '',
        inventoryname: '', // 名称
        usefor: '',
        shipname: '',
        vendorname: ''
      },
      loading: false,
      lineColor: ['#6699FF', '#E74823'],
      lineData: {
        xAxis: [],
        legend: [],
        smooth: 0,
        data: []
      },
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          title: '船舶',
          key: 'shipname',
          align: 'center',
          fixed: 'left',
          width: 90
        },
        {
          title: '入库日期',
          key: 'date',
          align: 'center',
          fixed: 'left',
          width: 100
        },
        {
          title: '入库单号',
          key: 'code',
          align: 'center',
          fixed: 'left',
          width: 130
        },
        {
          title: '存货编码',
          key: 'inventorycode',
          align: 'center',
          width: 85
        },
        {
          title: '规格型号',
          key: 'inventorystd',
          align: 'center',
          minWidth: 80
        },
        {
          title: '设备型号',
          key: 'unittype',
          align: 'center',
          minWidth: 80
        },
        {
          title: '单位',
          key: 'cmassunitname',
          align: 'center',
          width: 65
        },
        {
          title: '数量',
          key: 'quantity',
          align: 'center',
          minWidth: 80
        },
        {
          title: '原币含税单价(元)',
          key: 'price',
          align: 'center',
          minWidth: 80
        },
        {
          title: '原币价税合计(元)',
          key: 'iSum',
          align: 'center',
          minWidth: 80
        },
        {
          title: '设备生产厂家',
          key: 'producername',
          align: 'center',
          minWidth: 80
        },
        {
          title: '设备出厂编号',
          key: 'factorynumber',
          align: 'center',
          minWidth: 100
        }
      ],
      setSearchData: {
        date_month_st: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        date_month_et: {
          type: 'month_end',
          label: '-',
          selected: '',
          width: 100,
          value: '',
          isdisabled: false
        },
        usefor: {
          type: 'select',
          label: '用途',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true,
          change: this.changeUsefor
        },
        inventoryname: {
          type: 'select',
          label: '名称',
          selectData: [],
          unit: '',
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: false,
          change: this.changeInventoryname
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 130,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      }
    }
  },
  methods: {
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.queryParam.inventoryname = e.inventoryname
      this.queryParam.usefor = e.usefor
      this.queryParam.shipname = e.shipname
      this.queryParam.vendorname = e.vendorname
      this.curInventoryname = this.setSearchData.inventoryname.selected
      if (this.setSearchData.usefor.selected === undefined || this.setSearchData.usefor.selected === '') {
        this.$Message.error('用途不能为空！')
      } else if (this.setSearchData.inventoryname.selected === undefined || this.setSearchData.inventoryname.selected === '') {
        this.$Message.error('名称不能为空！')
      } else {
        this.getLineData()
        this.getList()
      }
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.date_month_st = e.key
      } else if (e.flag === 'month_end') {
        this.date_month_et = e.key
      }
    },
    // 获取数据
    async getData () {
      await this.getBaseData()
      await this.getCurData()
      this.getLineData()
      this.getList()
    },
    // 获取默认数据
    async getCurData () {
      let _curIdx = this.setSearchData.usefor.selectData.findIndex(item => { return item.value === '油料' })
      this.queryParam.usefor = this.setSearchData.usefor.selectData[_curIdx].value
      this.setSearchData.usefor.selected = this.setSearchData.usefor.selectData[_curIdx].value
      await this.changeUsefor(this.setSearchData.usefor)
      this.setSearchData.inventoryname.selected = this.setSearchData.inventoryname.selectData[1].value
      this.queryParam.inventoryname = this.setSearchData.inventoryname.selectData[1].value
      this.curInventoryname = this.setSearchData.inventoryname.selectData[1].value
    },
    // 获取拆线数据
    getLineData () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      this.lineData.xAxis = []
      this.lineData.data = []
      queryMaterialMonthPage(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.forEach(e => {
            this.lineData.xAxis.push(e.date_month)
            this.lineData.data.push(e.quantity_sum)
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取价格列表
    getList () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      this.loading = true
      Object.assign(this.listQuery, this.queryParam)
      Object.assign(this.listQuery, {
        order_by: 1 // 1按照时间排序；其它按金额排序
      })
      queryMaterialPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取基础信息
    async getBaseData () {
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname
            })
          })
        }
      })
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      await API.materialUsefor().then(res => { // 获取用途
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.usefor.selectData.push({
              value: item.usefor,
              label: item.usefor,
              key: item.key
            })
          })
        }
      })
    },
    // 获取系统时间
    async getSysDate () {
      if (Object.keys(this.$store.state.setState.monthMaterialParam).length > 0 && this.$store.state.setState.monthMaterialParam.date_month_st && this.$store.state.setState.monthMaterialParam.date_month_et) {
        // 获取存储的入参
        console.log(111)
        let storeQuery = this.$store.state.setState.monthMaterialParam
        this.setSearchData.date_month_st.selected = storeQuery.date_month_st
        this.setSearchData.date_month_et.selected = storeQuery.date_month_et
        this.date_month_st = storeQuery.date_month_st
        this.date_month_et = storeQuery.date_month_et
      } else {
        console.log(222)
        await API.getSysTime().then(res => {
          if (res.data.Code === 10000) {
            this.setSearchData.date_month_st.selected = res.data.now_date.substring(0, 5) + '01'
            this.setSearchData.date_month_et.selected = res.data.now_date.substring(0, 7)
            this.date_month_st = res.data.now_date.substring(0, 5) + '01'
            this.date_month_et = res.data.now_date.substring(0, 7)
          }
        })
      }
    },
    // 用途名称变化
    changeInventoryname (e) {
      let curUnitObj = e.selectData.filter(item => { return item.value === e.selected })
      this.curUnit = curUnitObj.length > 0 ? curUnitObj[0].unit : '吨'
    },
    // 根据用途获取关联名称
    async changeUsefor (e) {
      this.setSearchData.inventoryname.selectData = []
      this.setSearchData.inventoryname.selected = ''
      this.setSearchData.inventoryname.isdisabled = e.selected === undefined
      await API.materialInventoryname({ usefor: e.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.inventoryname.selectData.push({
              value: item.inventoryname,
              label: item.inventoryname,
              unit: item.cmassunitname
            })
          })
        }
      })
    },
    // 数据导出
    exportData (e) {
      exportMaterial(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 重置
    async resetResults () {
      this.queryParam.shipname = ''
      this.queryParam.vendorname = ''
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.shipname.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.$store.state.setState.monthMaterialParam = {}
      await this.getCurData()
      await this.getSysDate()
      await this.getLineData()
      await this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  },
  beforeDestroy() {
    this.$store.commit('setMonthMaterial', this.queryParam)
  },
  created () {
    this.getSysDate()
    this.getData()
  }
}
</script>
<style lang="less" scoped>
  .text_con {
    margin: 10px 0;
    button {
      float: right;
      color: #007DFF;
      font-size: 14px;
      font-weight: bold;
      margin-top: -5px;
      .ivu-icon {
        font-weight: bold;
      }
    }
  }
  .table_wrap {
    clear: both;
  }
</style>
