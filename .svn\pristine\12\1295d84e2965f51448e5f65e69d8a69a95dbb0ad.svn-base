import Vue from 'vue'
import Router from 'vue-router'
import routes from './routers'
import store from '@/store'
import iView from 'view-design'
import { canTurnTo, getToken, setTitle, setMenuShow, getMenuIndex } from '@/libs/util'
import config from '@/config'
const { homeName } = config

Vue.use(Router)
// 点击同一个路由报错问题  如果不采用此方法可以升级router至3.0 npm i vue-router@3.0 -S
const originalPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}
const router = new Router({
  routes,
  mode: 'history',
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})
const turnTo = (to, access, next) => {
  if (canTurnTo(to.name, access, routes)) next() // 有权限，可访问
  else next({ replace: true, name: 'error_401' }) // 无权限，重定向到401页面
}
const LOGIN_PAGE_NAME = 'login'
const UNLOGIN_PAGE_LIST = ['searchDetail', 'voyagePlanDetail'] // 无需登录校验页面

router.beforeEach((to, from, next) => {
  iView.LoadingBar.start()
  if (to.name === 'tabIframe') {
    store.commit('setMenuShow', false)
    setMenuShow(false)
    turnTo(to, store.state.user.access, next)
    return
  }
  const token = getToken()
  // 未登录且要跳转的页面不是登录页
  if (UNLOGIN_PAGE_LIST.includes(to.name)) { // 无需登录页 放行
    setMenuShow(false)
    // next()
    turnTo(to, store.state.user.access, next)
    return
  }
  if (!token && to.name === LOGIN_PAGE_NAME) {
    if (to.name !== LOGIN_PAGE_NAME) {
      next({
        name: LOGIN_PAGE_NAME // 跳转到登录页
      })
    } else {
      next()
    }
  } else if (token && to.name === LOGIN_PAGE_NAME) {
    // 已登录且要跳转的页面是登录页
    if (to.query.router) {
      if (document.referrer.includes('ierptest.xtshipping.com') || document.referrer.includes('erp.xtshipping.com')) {
        store.commit('setMenuShow', false)
        setMenuShow(false)
      } else {
        store.commit('setMenuShow', true)
        setMenuShow(true)
      }
      setTimeout(() => {
        if (to.query.router.indexOf('/') > 0) {
          next({
            name: to.query.router.split('/')[0], // 如果有指向跳向指定页面
            params: {
              id: to.query.router.split('/')[1]
            }
          })
        } else {
          if (to.query.router === 'erp' && (document.referrer.includes('ierptest.xtshipping.com') || document.referrer.includes('erp.xtshipping.com'))) { // 跳转至瀛海登录
            let _uuid = localStorage.getItem('uuid')
            window.open('http://erp.xtshipping.net/#/login?' + _uuid + '&resourceflag=1', '_self')
          } else {
            next({
              name: to.query.router // 如果有指向跳向指定页面
            })
          }
        }
      }, 100)
    } else {
      // 已登录且要跳转的页面是登录页
      next({
        name: homeName // 跳转到homeName页
      })
    }
  } else {
    // if (!token && !UNLOGIN_PAGE_LIST.includes(to.name)) {
    //   next({
    //     name: LOGIN_PAGE_NAME // 跳转到登录页
    //   })
    //   return
    // }
    if (UNLOGIN_PAGE_LIST.includes(to.name)) { //  || to.name === 'error_401'
      iView.LoadingBar.finish()
      window.scrollTo(0, 0)
      return
    }
    let status_value = JSON.parse(localStorage.getItem('userData')) && JSON.parse(localStorage.getItem('userData')).status_value
    if (status_value === '0') { // 离职跳转到登录页
      store.dispatch('handleLogOut')
      next({
        name: LOGIN_PAGE_NAME // 跳转到登录页
      })
      return
    }
    let allMenuList = JSON.parse(localStorage.getItem('allMenuList'))
    let isInMenuModule = false
    if (allMenuList && allMenuList.length > 0) {
      allMenuList.forEach(item => {
        let isCurModule
        if (to.name === 'externalLink') { // 当是外部链接时，匹配三方跳转url来获取当前模块
          isCurModule = getMenuIndex(item.children, to.query.url)
        } else {
          isCurModule = getMenuIndex(item.children, to.name)
        }
        if (isCurModule) { // 如果能找到表示有权限 后端返回的菜单内容
          isInMenuModule = true
          let homeRouter = {
            component: '',
            enabled: 1,
            href: '',
            icon: 'md-apps',
            menuDisplay: 1,
            mobileDisplay: 0,
            name: '首页',
            children: [{
              href: 'home',
              name: '首页',
              enabled: 1,
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-apps',
              component: () => import ('@/view/single-page/home')
            }]
          }
          // 特殊处理是备件管理栏目
          let isEquipModule = getMenuIndex(item.children, 'equipAdmin')
          if (isEquipModule) {
            store.commit('setAccess', 'erpSys')
          } else {
            store.commit('setAccess', 'visitor')
          }

          item.children.unshift(homeRouter)
          store.commit('setMenuList', JSON.stringify(item.children))
        }
      })
      let isManageLogin = ((localStorage.getItem('userDataId') === '9195D8D08B35449CA4AB1E4F545BDDD1' || JSON.parse(localStorage.getItem('userData')).admin_rights === '1'))
      let dynamicRouter = [ // 管理后台菜单路由
        {
          component: '',
          enabled: 1,
          href: '',
          icon: 'md-apps',
          menuDisplay: 1,
          mobileDisplay: 0,
          name: '首页',
          children: [{
            href: 'home',
            name: '首页',
            enabled: 1,
            menuDisplay: 1,
            mobileDisplay: 0,
            icon: 'md-apps',
            component: () => import ('@/view/single-page/home')
          }]
        },
        {
          href: 'jurisdictionManage',
          icon: 'ios-albums-outline',
          name: '组织架构',
          menuDisplay: 1,
          mobileDisplay: 0,
          children: [
            {
              href: 'userManagement',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-person',
              name: '用户管理',
              component: () => import('@/view/jurisdictionManage/userManagement')
            },
            {
              href: 'departmentManagement',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-people',
              name: '部门管理',
              component: () => import('@/view/jurisdictionManage/departmentManagement')
            },
            {
              href: 'moduleManagement',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-cog',
              name: '模块管理',
              component: () => import('@/view/jurisdictionManage/moduleManagement')
            }
          ]
        },
        {
          href: '/',
          menuDisplay: 1,
          mobileDisplay: 0,
          icon: 'ios-color-filter',
          name: '绩效管理',
          children: [
            {
              href: 'positionSystem',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-cog',
              name: '绩效设置',
              component: () => import('@/view/jurisdictionManage/positionSystem')
            },
            {
              href: 'performanceProfileManage',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'ios-folder-open',
              name: '绩效档案',
              component: () => import('@/view/jurisdictionManage/performanceProfile')
            },
            {
              href: 'performanceStatistic',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'ios-analytics',
              name: '绩效统计',
              component: () => import('@/view/jurisdictionManage/performanceStatistic')
            }
          ]
        },
        {
          href: 'pushSet',
          name: 'pushSet',
          icon: 'md-swap',
          name: '推送管理',
          menuDisplay: 1,
          mobileDisplay: 0,
          children: [
            {
              href: 'voyagePlanSet',
              name: '计划推送配置',
              icon: 'md-swap',
              menuDisplay: 1,
              mobileDisplay: 0,
              component: () => import('@/view/pushSet/voyagePlanSet')
            }
          ]
        }
      ]
      let isInRouter = getMenuIndex(dynamicRouter, to.name)
      if (isManageLogin && isInRouter) { // 在后台权限范围里的
        store.commit('setAccess', 'organizational')
        store.commit('setMenuList', JSON.stringify(dynamicRouter))
      }
    }
    if (!document.referrer.includes('ierptest.xtshipping.com') && !document.referrer.includes('erp.xtshipping.com')) {
      store.commit('setMenuShow', true)
      setMenuShow(true)
    }
    if (store.state.user.hasGetInfo) {
      turnTo(to, store.state.user.access, next)
    } else {
      turnTo(to, store.state.user.access, next)
    }
  }
})

router.afterEach(to => {
  setTitle(to, router.app)
  iView.LoadingBar.finish()
  window.scrollTo(0, 0)
})

export default router
