import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 推送配置列表查找 分页
export function queryVmpSendConfigPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/send/config/queryVmpSendConfigPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 推送配置列表查找 不分页
export function queryVmpSendConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/send/config/queryVmpSendConfigList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 推送配置添加
export function addVmpSendConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/send/config/addVmpSendConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 推送配置修改
export function updateVmpSendConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/send/config/updateVmpSendConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 推送配置删除
export function delVmpSendConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/send/config/delVmpSendConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryVmpSendConfigPage,
  queryVmpSendConfigList,
  addVmpSendConfig,
  updateVmpSendConfig,
  delVmpSendConfig
}