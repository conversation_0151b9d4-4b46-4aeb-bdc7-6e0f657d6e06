<template>
  <div>
    <div class="picker-area">
      <div class="picker-list">
        <span>时间：</span>
        <DatePicker type="month" placeholder="请选择月份" style="width: 100px" format="yyyy-MM" v-model="belong_month" @on-change="date => this.belong_month = date"></DatePicker>
      </div>
      <div class="picker-list">
        <span>模板：</span>
        {{ formTempName }}
      </div>
    </div>
    <div class="approve-top">
      <div v-for="(item, idx) in flowObj.flowList" :key="item.unified_account_id">
        <span v-if="flowObj.flowList.length > 1" class="approve-top-pre" :class="flowColor(idx)">{{ idx > 0 ? '审' : '自'}}</span>
        <span class="approve-top-name">{{ item.user_name }}</span>
        <Icon v-if="idx < (flowObj.flowList.length - 1)" class="approve-top-arrow" type="md-arrow-forward" color="#797979" size="20" />
      </div>
    </div>
    <h3>一、本月工作总结，分值90分。   质量评价：优秀100分、优良95分、符合要求：85分、基本符合80分；待改进：≤75分，完成率：工作进度完成率 </h3>
    <table class="table table-bordered" style="width: calc(100% - 180px)">
      <!-- 表头开始 -->
      <tr>
        <th rowspan="2" style="width: 50px;">序号</th>
        <th rowspan="2" style="width: 130px;">名称</th>
        <th rowspan="2">主要工作事项</th>
        <th rowspan="2">交付成果或完成情况描述</th>
        <th rowspan="2" style="width: 60px;">配分(85分)</th>
        <th colspan="2">完成率(60%)</th>
        <th colspan="2">质量评价(40%)</th>
        <th colspan="2">单项得分</th>
        <th rowspan="2">备注</th>
      </tr>
      <tr>
        <th style="width: 70px;">自评</th>
        <th style="width: 70px;">上级</th>
        <th style="width: 70px;">自评</th>
        <th style="width: 70px;">上级</th>
        <th style="width: 60px;">自评</th>
        <th style="width: 60px;">上级</th>
      </tr>
      <!-- 表头结束 -->

      <!-- 重点工作事项开始 -->
      <tr v-for="(item, idx) in curKeyList" :key="'key' + idx">
        <td class="center">{{ idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="curKeyList.length">{{ item.title }}</td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.content" />
          <span v-else>{{ item.content }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.result" />
          <span v-else>{{ item.result }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.score" />
          <span v-else>{{ item.score }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.selfRate" />
          <span v-else>{{ item.selfRate }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.supRate" />
          <span v-else>{{ item.supRate }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.selfQuality" />
          <span v-else>{{ item.selfQuality }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.supQuality" />
          <span v-else>{{ item.supQuality }}</span>
        </td>
        <td class="center">
          <span>{{ selfItemScore(item) }}</span>
        </td>
        <td class="center">
          <span>{{ supItemScore(item) }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (curKeyList.length - 1)" @click="addKeyList">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="curKeyList.length > 1" @click="removeKeyList(idx)">-</Button>
      </tr>
      <!-- 重点工作事项结束 -->

      <!-- 一般工作事项开始 -->
      <tr v-for="(item, idx) in curCommonList" :key="'common' + idx">
        <td class="center">{{ curKeyList.length + idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="curCommonList.length">{{ item.title }}</td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.content" />
          <span v-else>{{ item.content }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.result" />
          <span v-else>{{ item.result }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.score" />
          <span v-else>{{ item.score }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.selfRate" />
          <span v-else>{{ item.selfRate }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.supRate" />
          <span v-else>{{ item.supRate }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.selfQuality" />
          <span v-else>{{ item.selfQuality }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.supQuality" />
          <span v-else>{{ item.supQuality }}</span>
        </td>
        <td class="center">
          <span>{{ selfItemScore(item) }}</span>
        </td>
        <td class="center">
          <span>{{ supItemScore(item) }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (curCommonList.length - 1)" @click="addCommonList">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="curCommonList.length > 1" @click="removeCommonList(idx)">-</Button>
      </tr>
      <!-- 一般工作事项结束 -->
      <!-- 合计开始 -->
      <tr>
        <td class="center">{{ curKeyList.length + curCommonList.length + 1 }}</td>
        <td></td>
        <td></td>
        <td class="center">合计</td>
        <td class="center">{{ selfTotalScore() }}</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <!-- 合计结束 -->
      <tr>
        <td colspan="2" rowspan="2">加减分(5分，附加事项说明)</td>
        <td class="center">加分</td>
        <td colspan="6">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="extraObj.content" />
          <span v-else>{{ extraObj.content }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="extraObj.selfScore" />
          <span v-else>{{ extraObj.selfScore }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="extraObj.supScore" />
          <span v-else>{{ extraObj.supScore }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="extraObj.remark" />
          <span v-else>{{ extraObj.remark }}</span>
        </td>
      </tr>
      <tr>
        <td class="center">减分</td>
        <td colspan="6">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="reduceObj.content" />
          <span v-else>{{ reduceObj.content }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="reduceObj.selfScore" />
          <span v-else>{{ reduceObj.selfScore }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="reduceObj.supScore" />
          <span v-else>{{ reduceObj.supScore }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="reduceObj.remark" />
          <span v-else>{{ reduceObj.remark }}</span>
        </td>
      </tr>
      <tr>
        <td>加减分说明</td>
        <td colspan="7">
          <span v-html="addRemoveTxt"></span>
        </td>
        <td>小计</td>
        <td>{{ selfSubTotal() }}</td>
        <td>{{ supSubTotal() }}</td>
        <td></td>
      </tr>
    </table>
    <h3>二、本月工作小结，分值2分（上月计划调整未说明的，每件减1分）。</h3>
    <table class="table table-bordered" style="width: calc(100% - 180px)">
      <tr>
        <td style="width: 135px;">上月计划调整说明</td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="summaryObj.preMonthDesc" />
          <span v-else>{{ summaryObj.preMonthDesc }}</span>
        </td>
      </tr>
      <tr>
        <td style="width: 135px;">待改进事项(1分)</td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="summaryObj.inproveDesc" />
          <span v-else>{{ summaryObj.inproveDesc }}</span>
        </td>
      </tr>
      <tr>
        <td style="width: 135px;">改进对策与建议(1分)</td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="summaryObj.inproveSug" />
          <span v-else>{{ summaryObj.inproveSug }}</span>
        </td>
      </tr>
    </table>
    <h3>三、下月工作计划，分值8分。</h3>
    <table class="table table-bordered" style="width: calc(100% - 180px)">
      <tr>
        <th style="width: 50px;">序号</th>
        <th style="width: 145px;">名称</th>
        <th>工作事项</th>
        <th>交付成果或达成目标</th>
        <th style="width: 120px;">计划起止时间</th>
        <th>备注</th>
      </tr>
      <tr v-for="(item, idx) in planKeyList" :key="'key' + idx">
        <td class="center">{{ idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="planKeyList.length">{{ item.title }}</td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.content" />
          <span v-else>{{ item.content }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.result" />
          <span v-else>{{ item.result }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.time" />
          <span v-else>{{ item.time }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
        <Button class="remove_btn" size="small" type="error" v-if="planKeyList.length > 1" @click="removePlanKeyList(idx)">-</Button>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (planKeyList.length - 1)" @click="addPlanKeyList">+</Button>
      </tr>
      <tr v-for="(item, idx) in planCommonList" :key="'common' + idx">
        <td class="center">{{ curKeyList.length + idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="planCommonList.length">{{ item.title }}</td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.content" />
          <span v-else>{{ item.content }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.result" />
          <span v-else>{{ item.result }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.time" />
          <span v-else>{{ item.time }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
        <Button class="add_btn" size="small" type="primary" v-if="idx === (planCommonList.length - 1)" @click="addPlanCommonList">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="planCommonList.length > 1" @click="removePlanCommonList(idx)">-</Button>
      </tr>
    </table>
    <h3>四、月度考核总体评价</h3>
    <table class="table table-bordered" style="width: calc(100% - 180px)">
      <tr>
        <th style="width: 100px;">类别</th>
        <th>内容</th>
        <th style="width: 60px;">配分</th>
        <th style="width: 70px;">自评得分</th>
        <th style="width: 70px;">上级得分</th>
        <th>备注</th>
      </tr>
      <tr v-for="(item, idx) in examObj.curExamList" :key="'curExam' + idx">
        <td v-if="idx === 0" :rowspan="examObj.curExamList.length" class="center">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td class="center">
          <span>{{ item.score }}</span>
        </td>
        <td class="center">
          <Input v-if="idx > 0 && type === 'add' || type === 'modify'" class="list-input" v-model="item.selfScore" />
          <span v-else>{{ item.selfScore }}</span>
        </td>
        <td class="center">
          <Input v-if="idx > 0 && type === 'add' || type === 'modify'" class="list-input" v-model="item.supScore" />
          <span v-else>{{ item.supScore }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
      </tr>
      <tr v-for="(item, idx) in examObj.planExamList" :key="'planExam' + idx">
        <td v-if="idx === 0" :rowspan="examObj.planExamList.length" class="center">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td class="center">
          <span>{{ item.score }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.selfScore" />
          <span v-else>{{ item.selfScore }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.supScore" />
          <span v-else>{{ item.supScore }}</span>
        </td>
        <td>
          <Input v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
      </tr>
      <tr>
        <td colspan="2" class="center">小计</td>
        <td>{{ monthTotalScore() }}</td>
        <td>{{ monthSelfScore() }}</td>
        <td>{{ monthSupScore() }}</td>
        <td></td>
      </tr>
      <tr>
        <td>说明：</td>
        <td colspan="5"></td>
      </tr>
      <tr>
        <td colspan="6" v-html="examObj.explainTxt"></td>
      </tr>
    </table>
    <div class="btn-area">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" @click="handleSave">保存</Button>
    </div>
  </div>
</template>
<script>
import API from '@/api/performance'

export default ({
  data () {
    return {
      type: '',
      form_id: '',
      sysUser: '',
      belong_month: '', // 月份
      self_evaluate_score: '', // 自评总分
      re_evaluate_score: '', // 复评部分
      formTempName: '通用模板', // 模板名称
      sysIdx: null,
      flowObj: [], // 流程人员列表
      addRemoveTxt: '1、加减分说明内容加减分说明内容加减分说明内容<br> 2、加减分说明内容加减分说明内容加减分说明内容',
      extraObj: { // 加分项内容
        content: '加分内容说明',
        selfScore: '',
        supScore: '',
        remark: ''
      },
      reduceObj: { // 减分项内容
        content: '减分内容说明',
        selfScore: '',
        supScore: '',
        remark: ''
      },
      summaryObj: { // 本月小结
        preMonthDesc: '', // 上月计划说明
        inproveDesc: '', // 改进事项
        inproveSug: '' // 改进建议
      },
      curKeyList: [
        {
          title: '重点工作事项(60分)',
          content: '营运效率统计系统数据核对功能',
          result: '',
          score: '',
          selfRate: '',
          supRate: '',
          selfQuality: '',
          supQuality: '',
          selfScore: '',
          supScore: '',
          remark: ''
        },
        {
          title: '重点工作事项(60分)',
          content: '营运效率统计系统数据核对功能',
          result: '',
          score: '',
          selfRate: '',
          supRate: '',
          selfQuality: '',
          supQuality: '',
          selfScore: '',
          supScore: '',
          remark: ''
        },
        {
          title: '重点工作事项(60分)',
          content: '营运效率统计系统数据核对功能',
          result: '',
          score: '',
          selfRate: '',
          supRate: '',
          selfQuality: '',
          supQuality: '',
          selfScore: '',
          supScore: '',
          remark: ''
        }
      ],
      curCommonList: [
        {
          title: '一般工作事项(25分)',
          content: '',
          result: '',
          score: '',
          selfRate: '',
          supRate: '',
          selfQuality: '',
          supQuality: '',
          selfScore: '',
          supScore: '',
          remark: ''
        },
        {
          title: '一般工作事项(25分)',
          content: '',
          result: '',
          score: '',
          selfRate: '',
          supRate: '',
          selfQuality: '',
          supQuality: '',
          selfScore: '',
          supScore: '',
          remark: ''
        }
      ],
      planKeyList: [
        {
          title: '重点事项工作计划(5分)',
          content: '',
          result: '',
          time: '',
          remark: ''
        },
        {
          title: '重点事项工作计划(5分)',
          content: '',
          result: '',
          time: '',
          remark: ''
        }
      ],
      planCommonList: [
        {
          title: '一般事项工作计划(3分)',
          content: '',
          result: '',
          time: '',
          remark: ''
        },
        {
          title: '一般事项工作计划(3分)',
          content: '',
          result: '',
          time: '',
          remark: ''
        }
      ],
      examObj: {
        totalScore: '100',
        explainTxt: '说明：1、本月总结与计划工作表共100分，其中：工作总结90分、工作小结2分、工作计划8分;<br>2、质量评价参考标准：优秀(100分)：有创新、或效率有大幅提升或成果被推广应用或受领导或客户表扬等超出预期的工作质量，优良(95分)：工作/服务质量比以往有一定提升和改进、工作/服务质量好于预期，符合要求(85分)：质量与以往相同，基本能达到要求;基本符合(80分)：质量稍有差距，或略加指导能满足要求，待改进(75分)：质量有较大差距或需经较大调整或3次以上改进才能满足要求。<br>3、单项自评得分=配分*(自评完成率*60% + 质量自评%*40%),单项上级评价得分=配分*(自评完成率*60%+上级质量评分%*40%)<br>4、本月工作小结共2分，原计划调整未说明情况的，每项扣1分;本月提供待改进事项和改进对策与建议，在下月考核中整改落实到位或建议得到实施的加1分;',
        curExamList: [
          {
            title: '本月总结',
            content: '本月工作总结',
            score: '90',
            selfScore: '',
            supScore: '',
            remark: ''
          },
          {
            title: '本月总结',
            content: '待改进事项',
            score: '1',
            selfScore: '',
            supScore: '',
            remark: ''
          },
          {
            title: '本月总结',
            content: '改进对策与建议',
            score: '1',
            selfScore: '',
            supScore: '',
            remark: ''
          }
        ],
        planExamList: [
          {
            title: '下月计划',
            content: '下月重点事项计划',
            score: '5',
            selfScore: '',
            supScore: '',
            remark: ''
          },
          {
            title: '下月计划',
            content: '下月一般事项计划',
            score: '3',
            selfScore: '',
            supScore: '',
            remark: ''
          }
        ]
      }
    }
  },
  methods: {
    getFormDetail () { // 获取详情
      API.getPerfFormInfo({ form_id: this.form_id }).then(res => {
        if (res.data.Code === 10000) {
          let _formObj = res.data.Result[0].form_json
          if (JSON.stringify(_formObj) === '{}') return
          this.curKeyList = _formObj.curKeyList
          this.curCommonList = _formObj.curCommonList
          this.extraObj = _formObj.extraObj
          this.reduceObj = _formObj.reduceObj
          this.summaryObj = _formObj.summaryObj
          this.planKeyList = _formObj.planKeyList
          this.planCommonList = _formObj.planCommonList
          this.examObj = _formObj.examObj
          this.belong_month = res.data.Result[0].belong_month
        }
      })
    },
    flowColor (idx) { // 顶部流程标题颜色
      let _backStr = 'back-flow-color'
      if (this.sysIdx === idx) {
        _backStr = 'cur-flow-color'
      }
      if (this.sysIdx > idx) {
        _backStr = 'pre-flow-color'
      }
      if (this.sysIdx < idx) {
        _backStr = 'back-flow-color'
      }
      return _backStr
    },
    addKeyList () { // 添加重点事项
      this.curKeyList.push({
        title: '重点工作事项(60分)',
        content: '',
        result: '',
        score: '',
        selfRate: '',
        supRate: '',
        selfQuality: '',
        supQuality: '',
        selfScore: '',
        supScore: '',
        remark: ''
      })
    },
    removeKeyList (idx) { // 删除重点事项
      this.curKeyList.splice(idx, 1)
    },
    addCommonList () { // 添加一般工作事项
      this.curCommonList.push({
        title: '一般工作事项(25分)',
        content: '',
        result: '',
        score: '',
        selfRate: '',
        supRate: '',
        selfQuality: '',
        supQuality: '',
        selfScore: '',
        supScore: '',
        remark: ''
      })
    },
    removeCommonList (idx) { // 移除一般工作事项
      this.curCommonList.splice(idx, 1)
    },
    addPlanKeyList () { // 添加计划重点事项
      this.planKeyList.push({
        title: '重点事项工作计划(5分)',
        content: '',
        result: '',
        time: '',
        remark: ''
      })
    },
    removePlanKeyList (idx) { // 移除计划重点事项
      this.planKeyList.splice(idx, 1)
    },
    addPlanCommonList () { // 添加计划一般事项
      this.planCommonList.push({
        title: '一般事项工作计划(3分)',
        content: '',
        result: '',
        time: '',
        remark: ''
      })
    },
    removePlanCommonList (idx) { // 移除计划一般事项
      this.planCommonList.splice(idx, 1)
    },
    selfTotalScore () { // 合计
      let keyTotal = 0
      let commonTotal = 0
      this.curKeyList.map(item => {
        keyTotal += (isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      })
      this.curCommonList.map(item => {
        commonTotal += (isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      })
      return (keyTotal + commonTotal)
    },
    selfSubTotal () { // 自评小计
      let keyTotal = 0
      let commonTotal = 0
      this.curKeyList.map(item => {
        keyTotal += (isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.curCommonList.map(item => {
        commonTotal += (isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      let extraSelfScore = (isNaN(this.extraObj.selfScore) || this.extraObj.selfScore === '') ? 0 : parseFloat(this.extraObj.selfScore)
      let reduceSelfScore = (isNaN(this.reduceObj.selfScore) || this.reduceObj.selfScore === '') ? 0 : parseFloat(this.reduceObj.selfScore)
      let backScore = keyTotal + commonTotal + extraSelfScore + reduceSelfScore
      this.examObj.curExamList[0].selfScore = backScore.toFixed(1)
      return backScore.toFixed(1)
    },
    supSubTotal () { // 上级小计
      let keyTotal = 0
      let commonTotal = 0
      this.curKeyList.map(item => {
        keyTotal += (isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      this.curCommonList.map(item => {
        commonTotal += (isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      let extraSupScore = (isNaN(this.extraObj.supScore) || this.extraObj.supScore === '') ? 0 : parseFloat(this.extraObj.supScore)
      let reduceSupScore = (isNaN(this.reduceObj.supScore) || this.reduceObj.supScore === '') ? 0 : parseFloat(this.reduceObj.supScore)
      this.examObj.curExamList[0].supScore = (keyTotal + commonTotal + extraSupScore + reduceSupScore).toFixed(1)
      return (keyTotal + commonTotal + extraSupScore + reduceSupScore).toFixed(1)
    },
    selfItemScore (item) { // 本月总结重点项目自评单项得分
      let _score = (item.score === '' ? 0 : parseInt(item.score)) * ((item.selfRate === '' ? 0 : parseInt(item.selfRate)) / 100 * 0.6 + (item.selfQuality === '' ? 0 : parseInt(item.selfQuality)) / 100 * 0.4)
      if (isNaN(_score)) {
        return 0
      }
      Object.assign(item, {
        selfScore: _score.toFixed(1)
      })
      return _score.toFixed(1)
    },
    supItemScore (item) { // 本月总结重点项目上级单项得分
      let _score = (item.score === '' ? 0 : parseInt(item.score)) * ((item.supRate === '' ? 0 : parseInt(item.supRate)) / 100 * 0.6 + (item.supQuality === '' ? 0 : parseInt(item.supQuality)) / 100 * 0.4)
      if (isNaN(_score)) {
        return 0
      }
      Object.assign(item, {
        supScore: _score.toFixed(1)
      })
      return _score.toFixed(1)
    },
    monthTotalScore () { // 月度考核总配分
      let _curScore = 0
      let _planScore = 0
      this.examObj.curExamList.map(item => {
        _curScore += (isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      })
      this.examObj.planExamList.map(item => {
        _planScore += (isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      })
      return (_curScore + _planScore)
    },
    monthSelfScore () { // 月度考核自评得分
      let _curScore = 0
      let _planScore = 0
      this.examObj.curExamList.map(item => {
        _curScore += (isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.examObj.planExamList.map(item => {
        _planScore += (isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.self_evaluate_score = (_curScore + _planScore)
      return (_curScore + _planScore)
    },
    monthSupScore () { // 月度考核上级得分
      let _curScore = 0
      let _planScore = 0
      this.examObj.curExamList.map(item => {
        _curScore += (isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      this.examObj.planExamList.map(item => {
        _planScore += (isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      this.re_evaluate_score = (_curScore + _planScore)
      return (_curScore + _planScore)
    },
    handleSave () { // 保存
      let _formJson = {
        curKeyList: this.curKeyList,
        curCommonList: this.curCommonList,
        extraObj: this.extraObj,
        reduceObj: this.reduceObj,
        summaryObj: this.summaryObj,
        planKeyList: this.planKeyList,
        planCommonList: this.planCommonList,
        examObj: this.examObj
      }
      API.addOrUpdatePerfForm({
        unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        dept_flow_id: JSON.parse(localStorage.getItem('userFlow')).dept_flow_id,
        belong_month: this.belong_month,
        self_evaluate_score: this.self_evaluate_score,
        re_evaluate_score: this.re_evaluate_score,
        form_json: JSON.stringify(_formJson)
      }).then(res => {
        if (res.data.Code === 10000) {

        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    handleCancel () { // 取消321

    }
  },
  created () {
    this.type = this.$route.params.id
    this.sysUser = JSON.parse(localStorage.getItem('userData')).user_name
    this.flowObj = JSON.parse(localStorage.getItem('userFlow'))
    if (this.flowObj.form_num === '0') this.formTempName = '通用模板'
    if (this.flowObj.form_num === '1') this.formTempName = '业务模板'
    if (this.flowObj.form_num === '2') this.formTempName = '采购模板'
    if (this.flowObj.flowList && this.flowObj.flowList.length > 0) {
      this.flowObj.flowList.reverse() // 数组倒序
      this.sysIdx = this.flowObj.flowList.findIndex(item => item.user_name === this.sysUser)
    }
    if (this.type !== 'add') { // 编辑或者详情
      let _backArr = this.type.split('&id=')
      this.type = _backArr[0]
      this.form_id = _backArr[1]
      this.getFormDetail()
    }
  }
})
</script>
<style lang="less">
  .add_btn {
    position: absolute;
    right: -55px;
  }
  .remove_btn {
    position: absolute;
    right: -25px;
  }
  .table-bordered {
    border-left:2px solid #e8eaec;
    border-top:2px solid #e8eaec;
  }
  .table-bordered tr {
    position: relative;
  }
  .table-bordered th{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    text-align: center;
    vertical-align: middle;
    padding: 0 10px;
    input {
      color: #999;
    }
  }
  .table-bordered td{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
    padding: 0 10px;
  }
  .table-bordered .center {
    text-align: center;
  }
  .picker-area {
    display: flex;
    margin-bottom: 20px;
    align-items: center;
  }
  .picker-list {
    margin-right: 30px;
  }
  .picker-list span {
    margin-right: 5px;
    font-weight: bold;
  }
  .approve-top {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
  }
  .approve-top-pre {
    color: #fff;
    width: 30px;
    height: 30px;
    padding: 5px;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    border-radius: 15px;
    border: 2px solid #fff;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
  }
  .approve-top-name {
    margin-left: 12px;
  }
  .approve-top-arrow {
    margin: 0 10px;
  }
  .cur-flow-color {
    background: #D9001B;
  }
  .back-flow-color {
    background: #aaa;
  }
  .pre-flow-color {
    background: #70B603;
  }
  .list-input .ivu-input {
    height: 20px !important;
  }
  .btn-area {
    text-align: right;
    margin-top: 20px;
  }
  .btn-area button {
    margin-left: 15px;
  }
</style>
