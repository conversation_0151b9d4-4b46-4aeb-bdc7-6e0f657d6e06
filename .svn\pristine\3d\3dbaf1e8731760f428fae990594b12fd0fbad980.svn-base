<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="300" @on-visible-change="modalShowHide" :mask-closable="false" ok-text="保存" @on-ok=
  "addHandle">
    <Form ref="formValide" :model="modalData" :label-width="45" style="margin-bottom: 20px;">
      <FormItem label="名称">
        <span><Input type='text' v-model='modalData.dept_name'></Input></span>
      </FormItem>
    </Form>
  </Modal>
</template>
<script>
import API from '@/api/jurisdictionManage/departManagement'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {

    }
  },
  methods: {
    // 添加部门
    addHandle () {
      API.addDepartManagePage({
        dept_name: this.modalData.dept_name
      }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.$emit('modalDataBack')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 弹窗显隐
    modalShowHide (val) {
      if (val) {
      }
    }
  }
})
</script>
