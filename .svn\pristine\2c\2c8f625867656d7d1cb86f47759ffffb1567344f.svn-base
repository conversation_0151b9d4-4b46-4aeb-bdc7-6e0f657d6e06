import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 对比分析
export function getMaterialCompare (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/getUnifiedMaterialUseforGroupCompare',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 数据导出
export function materialCompareExport (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/getUnifiedMaterialUseforGroupCompareExport',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}
