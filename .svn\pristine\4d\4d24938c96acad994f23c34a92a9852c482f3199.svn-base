<template>
  <!-- 已计划 统计 -->
  <Drawer :title="fileModalData.title" v-model="fileModalData.modal" :data="fileModalData.data" @on-visible-change="modalShow" width="850">
    <h3 class="table_headinfo">文件<span>支持word、PPT、Excel</span></h3>
    <fileUpload v-if="fileModalData.modal" ref="fileUploadComponent" :fileDataList="fileDataList" :themeId="themeId"></fileUpload>
    <div class="demo-drawer-footer">
      <Button type="primary" @click="fileModalData.modal = false">返回</Button>
    </div>
  </Drawer>
</template>
<script>
import fileUpload from './file-upload' // 文件上传组件
import { queryAttachmentList } from '@/api/examSystem/trainingModule/trainingTopics'
export default {
  props: {
    fileModalData: Object
  },
  components: {
    fileUpload
  },
  data () {
    return {
      themeId: '',
      fileDataList: [],
      listQuery: {
        theme_attachment_id: '',
        theme_id: ''
      }
    }
  },
  methods: {
    // 获取附件列表
    getList () {
      queryAttachmentList(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.fileDataList = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    modalShow (val) {
      if (val) {
        this.listQuery.theme_id = this.fileModalData.data.theme_id
        this.themeId = this.fileModalData.data.theme_id
        this.getList()
      } else {
        this.fileDataList = []
        this.listQuery = {
          theme_attachment_id: '',
          theme_id: ''
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.table_headinfo {
  margin-bottom: 15px;
  color: #333;
}
</style>
