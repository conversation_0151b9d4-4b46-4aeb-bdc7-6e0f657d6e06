<template>
  <div>
    <div style="background: #fff;">
      <Tabs>
        <TabPane label="设备管理">
          <Row>
            <Col span="6">
              <Tree :data="equipTree" :render="renderContent"></Tree>
            </Col>
            <Col span="18">
              <Table border  :columns="columns" :data="equipList"></Table>
            </Col>
          </Row>
        </TabPane>
        <TabPane label="设备分类"></TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      columns: [
        {
          title: '序号',
          key: '',
          align: 'center',
          width: 70
        },
        {
          title: '设备名称',
          key: '',
          align: 'center'
        },
        {
          title: '设备型号',
          key: '',
          align: 'center'
        },
        {
          title: '制造厂商',
          key: '',
          align: 'center'
        },
        {
          title: '出厂编号',
          key: '',
          align: 'center'
        },
        {
          title: '图纸编号',
          key: '',
          align: 'center'
        },
        {
          title: '生产日期',
          key: '',
          align: 'center'
        },
        {
          title: '设备级别',
          key: 'level',
          align: 'center',
          filters: [
            {
              label: '一般设备',
              value: '一般'
            },
            {
              label: '安全设备',
              value: '安全'
            },
            {
              label: '关键设备',
              value: '关键'
            },
            {
              label: '应急设备',
              value: '应急'
            }
          ],
          fileterMethod (value, row) {
            return row.level.indexOf(value) > -1
          }
        },
        {
          title: '设备状态',
          key: 'status',
          align: 'center',
          filters: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 2
            }
          ],
          filterMultiple: false,
          fileterMethod (value, row) {
            if (value === 1) {
              return row.status === 1
            } else if (value === 2) {
              return row.status === 2
            }
          }
        },
        {
          title: '附件',
          key: '',
          align: 'center',
          width: 120
        }
      ],
      equipTree: [
        {
          title: '主推及辅助设备类',
          // expand: true,
          render: (h, { root, node, data }) => {
            return h('span', {
              style: {
                display: 'inline-block',
                width: '100%'
              },
              on: {
                click: () => {
                  console.log(data)
                }
              }
            }, [
              h('span', [
                h('Icon', {
                  props: {
                    type: 'md-home'
                  },
                  style: {
                    marginRight: '8px'
                  }
                }),
                h('span', data.title)
              ])
            ])
          },
          children: [
            {
              title: '主机'
            },
            {
              title: '供油单元'
            },
            {
              title: '淡水冷却器'
            },
            {
              title: '滑油冷却器'
            }
          ]
        }
      ],
      equipList: [] // 设备列表
    }
  },
  methods: {
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
          click: () => {
            console.log(data)
          }
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', data.title)
        ])
      ])
    }
  }
}
</script>
